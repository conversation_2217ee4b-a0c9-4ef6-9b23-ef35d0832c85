const mongoose = require('mongoose');

const couponSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  type: {
    type: Number,
    required: true
    // 1: 满减券
    // 2: 折扣券
  },
  amount: {
    type: Number,
    required: true
  },
  minAmount: {
    type: Number,
    default: 0
  },
  startTime: {
    type: Date,
    required: true
  },
  endTime: {
    type: Date,
    required: true
  },
  description: String,
  status: {
    type: Number,
    default: 1
    // 0: 已禁用
    // 1: 正常
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('Coupon', couponSchema); 
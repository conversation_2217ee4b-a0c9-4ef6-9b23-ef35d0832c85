// 分包/profile/address/list.js
import { api } from '../../../工具/api'
import { showToast, showLoading, hideLoading } from '../../../工具/util'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    addressList: [],
    loading: false,
    from: '' // 来源页面，如果是从订单页面来的，选择地址后需要返回
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.from) {
      this.setData({ from: options.from })
    }
    this.loadAddressList()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时刷新列表
    this.loadAddressList()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadAddressList().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  async loadAddressList() {
    if (this.data.loading) return
    
    try {
      showLoading()
      this.setData({ loading: true })
      
      const res = await api.user.getAddresses()
      if (res.success) {
        this.setData({ 
          addressList: res.data,
          loading: false
        })
      }
    } catch (error) {
      console.error('获取地址列表失败：', error)
      showToast('获取地址列表失败')
      this.setData({ loading: false })
    } finally {
      hideLoading()
    }
  },

  // 添加新地址
  addAddress() {
    wx.navigateTo({
      url: './edit/index'
    })
  },

  // 编辑地址
  editAddress(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `./edit/index?id=${id}`
    })
  },

  // 删除地址
  async deleteAddress(e) {
    const { id } = e.currentTarget.dataset
    try {
      const confirmed = await wx.showModal({
        title: '提示',
        content: '确定要删除该地址吗？',
        confirmText: '删除',
        confirmColor: '#ff4444'
      })
      
      if (confirmed.confirm) {
        const res = await api.user.deleteAddress(id)
        if (res.success) {
          showToast('删除成功', 'success')
          // 更新列表
          const addressList = this.data.addressList.filter(item => item.id !== id)
          this.setData({ addressList })
        }
      }
    } catch (error) {
      console.error('删除地址失败：', error)
      showToast('删除失败')
    }
  },

  // 设置默认地址
  async setDefaultAddress(e) {
    const { id } = e.currentTarget.dataset
    try {
      const res = await api.user.updateAddress(id, { isDefault: true })
      if (res.success) {
        showToast('设置成功', 'success')
        // 更新列表状态
        const addressList = this.data.addressList.map(item => ({
          ...item,
          isDefault: item.id === id
        }))
        this.setData({ addressList })
      }
    } catch (error) {
      console.error('设置默认地址失败：', error)
      showToast('设置失败')
    }
  },

  // 选择地址
  selectAddress(e) {
    if (!this.data.from) return
    
    const { id } = e.currentTarget.dataset
    const address = this.data.addressList.find(item => item.id === id)
    
    // 返回上一页并传递地址信息
    const pages = getCurrentPages()
    const prevPage = pages[pages.length - 2]
    
    prevPage.setData({
      selectedAddress: address
    })
    
    wx.navigateBack()
  },

  // 阻止事件冒泡
  stopPropagation() {
    return
  }
})
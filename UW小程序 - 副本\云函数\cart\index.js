// 云函数入口文件
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;
const $ = db.command.aggregate;

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event;
  const { OPENID } = cloud.getWXContext();
  
  switch (action) {
    case 'add':
      return await addToCart(OPENID, data);
    case 'update':
      return await updateCart(OPENID, data);
    case 'remove':
      return await removeFromCart(OPENID, data);
    case 'list':
      return await getCartList(OPENID);
    default:
      throw new Error('未知的操作类型');
  }
};

// 添加到购物车
async function addToCart(openid, data) {
  const { productId, skuId, quantity } = data;
  
  try {
    // 获取商品信息
    const product = await db.collection('products')
      .doc(productId)
      .get();
      
    if (!product.data) {
      return {
        success: false,
        message: '商品不存在'
      };
    }
    
    const sku = product.data.skus.find(s => s._id === skuId);
    if (!sku) {
      return {
        success: false,
        message: '商品规格不存在'
      };
    }
    
    if (sku.stock < quantity) {
      return {
        success: false,
        message: '商品库存不足'
      };
    }
    
    // 检查是否已存在相同商品和SKU
    const cartItem = await db.collection('cart')
      .where({
        openid,
        productId,
        'sku._id': skuId
      })
      .get();
      
    if (cartItem.data.length > 0) {
      // 更新数量
      const newQuantity = cartItem.data[0].quantity + quantity;
      if (newQuantity > sku.stock) {
        return {
          success: false,
          message: '商品库存不足'
        };
      }
      
      await db.collection('cart')
        .doc(cartItem.data[0]._id)
        .update({
          data: {
            quantity: newQuantity
          }
        });
    } else {
      // 添加新购物车项
      await db.collection('cart').add({
        data: {
          openid,
          productId,
          product: {
            name: product.data.name,
            imageUrl: product.data.images[0]
          },
          sku: {
            _id: sku._id,
            name: sku.name,
            price: sku.price,
            imageUrl: sku.imageUrl
          },
          quantity,
          selected: true,
          createdAt: db.serverDate()
        }
      });
    }
    
    return {
      success: true,
      message: '添加成功'
    };
  } catch (error) {
    return {
      success: false,
      message: error.message
    };
  }
}

// 更新购物车
async function updateCart(openid, data) {
  const { id, quantity, selected } = data;
  
  try {
    const updateData = {};
    
    if (quantity !== undefined) {
      // 检查库存
      const cartItem = await db.collection('cart')
        .doc(id)
        .get();
        
      if (!cartItem.data) {
        return {
          success: false,
          message: '购物车商品不存在'
        };
      }
      
      const product = await db.collection('products')
        .doc(cartItem.data.productId)
        .get();
        
      if (!product.data) {
        return {
          success: false,
          message: '商品不存在'
        };
      }
      
      const sku = product.data.skus.find(s => s._id === cartItem.data.sku._id);
      if (!sku) {
        return {
          success: false,
          message: '商品规格不存在'
        };
      }
      
      if (sku.stock < quantity) {
        return {
          success: false,
          message: '商品库存不足'
        };
      }
      
      updateData.quantity = quantity;
    }
    
    if (selected !== undefined) {
      updateData.selected = selected;
    }
    
    await db.collection('cart')
      .doc(id)
      .update({
        data: updateData
      });
      
    return {
      success: true,
      message: '更新成功'
    };
  } catch (error) {
    return {
      success: false,
      message: error.message
    };
  }
}

// 从购物车移除
async function removeFromCart(openid, data) {
  const { id } = data;
  
  try {
    await db.collection('cart')
      .doc(id)
      .remove();
      
    return {
      success: true,
      message: '删除成功'
    };
  } catch (error) {
    return {
      success: false,
      message: error.message
    };
  }
}

// 获取购物车列表
async function getCartList(openid) {
  try {
    const cartItems = await db.collection('cart')
      .aggregate()
      .match({
        openid
      })
      .lookup({
        from: 'products',
        localField: 'productId',
        foreignField: '_id',
        as: 'productDetail'
      })
      .project({
        _id: 1,
        product: 1,
        sku: 1,
        quantity: 1,
        selected: 1,
        createdAt: 1,
        productDetail: $.arrayElemAt(['$productDetail', 0])
      })
      .end();
      
    return {
      success: true,
      data: cartItems.list
    };
  } catch (error) {
    return {
      success: false,
      message: error.message
    };
  }
} 
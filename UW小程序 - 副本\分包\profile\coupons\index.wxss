/* 分包/profile/coupons/index.wxss */
.container {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 标签页样式 */
.tabs {
  display: flex;
  background: #fff;
  padding: 0 30rpx;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1;
}

.tab-item {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.tab-item text {
  font-size: 28rpx;
  color: #333;
}

.tab-item.active text {
  color: #000;
  font-weight: bold;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #000;
  border-radius: 2rpx;
}

/* 优惠券列表样式 */
.coupon-list {
  flex: 1;
  margin-top: 88rpx;
  padding: 20rpx;
}

.coupon-item {
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.coupon-item.disabled {
  opacity: 0.6;
}

.coupon-left {
  width: 220rpx;
  background: #ff4444;
  color: #fff;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.coupon-left::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 20rpx;
  background: radial-gradient(circle at right, transparent 0, transparent 10rpx, #fff 10rpx);
  background-size: 20rpx 40rpx;
  background-repeat: repeat-y;
}

.amount {
  display: flex;
  align-items: baseline;
}

.symbol {
  font-size: 32rpx;
}

.number {
  font-size: 64rpx;
  font-weight: bold;
  margin-left: 4rpx;
}

.condition {
  font-size: 24rpx;
  margin-top: 10rpx;
}

.coupon-right {
  flex: 1;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.coupon-info {
  flex: 1;
}

.coupon-name {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.coupon-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.coupon-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.coupon-action {
  margin-top: 20rpx;
  text-align: right;
}

.use-btn {
  display: inline-block;
  height: 60rpx;
  line-height: 60rpx;
  padding: 0 30rpx;
  font-size: 26rpx;
  color: #fff;
  background: #ff4444;
  border-radius: 30rpx;
}

.use-btn.disabled {
  background: #ccc;
}

.status-text {
  font-size: 26rpx;
  color: #999;
}

/* 加载状态样式 */
.loading-status {
  text-align: center;
  padding: 30rpx 0;
}

.loading, .no-more {
  font-size: 24rpx;
  color: #999;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
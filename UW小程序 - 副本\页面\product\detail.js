const { productApi, cart<PERSON>pi } = require('../../工具/api');
const { showToast, showLoading, hideLoading, showModal } = require('../../工具/util');
const store = require('../../状态管理/store');

Page({
  data: {
    id: '',
    product: null,
    selectedSku: null,
    quantity: 1,
    comments: [],
    relatedProducts: [],
    loading: false,
    showSkuPopup: false
  },

  onLoad(options) {
    const { id } = options;
    this.setData({ id });
    this.loadData();
  },

  // 加载所有数据
  async loadData() {
    if (this.data.loading) return;
    
    try {
      showLoading();
      this.setData({ loading: true });
      
      await Promise.all([
        this.loadProductDetail(),
        this.loadComments(),
        this.loadRelatedProducts()
      ]);
    } catch (error) {
      console.error('加载数据失败:', error);
      showToast('加载失败，请重试');
    } finally {
      hideLoading();
      this.setData({ loading: false });
    }
  },

  // 加载商品详情
  async loadProductDetail() {
    try {
      const product = await productApi.getProductDetail(this.data.id);
      this.setData({ 
        product,
        selectedSku: product.skus[0] // 默认选择第一个SKU
      });
    } catch (error) {
      console.error('加载商品详情失败:', error);
      throw error;
    }
  },

  // 加载评论
  async loadComments() {
    try {
      const comments = await productApi.getProductComments(this.data.id, { page: 1, limit: 5 });
      this.setData({ comments });
    } catch (error) {
      console.error('加载评论失败:', error);
      throw error;
    }
  },

  // 加载相关商品
  async loadRelatedProducts() {
    try {
      const relatedProducts = await productApi.getRelatedProducts(this.data.id);
      this.setData({ relatedProducts });
    } catch (error) {
      console.error('加载相关商品失败:', error);
      throw error;
    }
  },

  // 选择SKU
  onSelectSku(e) {
    const { sku } = e.currentTarget.dataset;
    this.setData({ selectedSku: sku });
  },

  // 修改数量
  onQuantityChange(e) {
    const quantity = parseInt(e.detail.value);
    if (quantity > 0 && quantity <= this.data.selectedSku.stock) {
      this.setData({ quantity });
    }
  },

  // 显示SKU选择弹窗
  showSkuPopup() {
    this.setData({ showSkuPopup: true });
  },

  // 隐藏SKU选择弹窗
  hideSkuPopup() {
    this.setData({ showSkuPopup: false });
  },

  // 添加到购物车
  async addToCart() {
    if (!store.getState().userInfo) {
      wx.navigateTo({ url: '/页面/login/index' });
      return;
    }

    try {
      showLoading('添加中...');
      
      await cartApi.addToCart({
        productId: this.data.id,
        skuId: this.data.selectedSku.id,
        quantity: this.data.quantity
      });

      showToast('添加成功', 'success');
      this.hideSkuPopup();
      
      // 更新购物车数量
      const cartList = await cartApi.getCartList();
      store.setCartCount(cartList.length);
    } catch (error) {
      console.error('添加到购物车失败:', error);
      showToast('添加失败，请重试');
    } finally {
      hideLoading();
    }
  },

  // 立即购买
  async buyNow() {
    if (!store.getState().userInfo) {
      wx.navigateTo({ url: '/页面/login/index' });
      return;
    }

    // 跳转到确认订单页面
    wx.navigateTo({
      url: `/页面/order/confirm/index?productId=${this.data.id}&skuId=${this.data.selectedSku.id}&quantity=${this.data.quantity}`
    });
  },

  // 分享
  onShareAppMessage() {
    const { product } = this.data;
    return {
      title: product.name,
      imageUrl: product.images[0],
      path: `/页面/product/detail?id=${product.id}`
    };
  }
}); 
import { observable, action } from 'mobx-miniprogram';
import { api } from '../utils/api';

export const store = observable({
  // 用户状态
  userInfo: null,
  isLoggedIn: false,

  // 购物车状态
  cartItems: [],
  cartTotal: 0,

  // Actions
  updateUserInfo: action(function(info) {
    this.userInfo = info;
    this.isLoggedIn = !!info;
    // 持久化存储
    wx.setStorageSync('userInfo', info);
  }),

  // 购物车操作
  addToCart: action(async function(product, sku, quantity = 1) {
    try {
      await api.cart.add({
        productId: product._id,
        sku,
        quantity
      });
      
      wx.showToast({
        title: '添加成功',
        icon: 'success'
      });
    } catch (error) {
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      });
    }
  }),

  removeFromCart: action(function(productId) {
    this.cartItems = this.cartItems.filter(item => item.id !== productId);
    this.updateCartTotal();
    this.saveCartToStorage();
  }),

  updateCartItemQuantity: action(function(productId, quantity) {
    const item = this.cartItems.find(item => item.id === productId);
    if (item) {
      item.quantity = quantity;
      this.updateCartTotal();
      this.saveCartToStorage();
    }
  }),

  clearCart: action(function() {
    this.cartItems = [];
    this.cartTotal = 0;
    wx.removeStorageSync('cartItems');
  }),

  // 辅助方法
  updateCartTotal: action(function() {
    this.cartTotal = this.cartItems.reduce((total, item) => {
      return total + (item.price * item.quantity);
    }, 0);
  }),

  saveCartToStorage: action(function() {
    wx.setStorageSync('cartItems', this.cartItems);
  }),

  // 初始化方法
  init: action(function() {
    // 恢复购物车数据
    const cartItems = wx.getStorageSync('cartItems') || [];
    this.cartItems = cartItems;
    this.updateCartTotal();
    
    // 恢复用户信息
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.updateUserInfo(userInfo);
    }
  })
}); 
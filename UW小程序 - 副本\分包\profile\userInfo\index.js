// subPackage/profile/userInfo/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({ userInfo })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 修改头像
  changeAvatar() {
    wx.chooseImage({
      count: 1,
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0]
        const userInfo = { ...this.data.userInfo, avatarUrl: tempFilePath }
        this.updateUserInfo(userInfo)
      },
      fail: () => {
        wx.showToast({
          title: '请授权相册权限',
          icon: 'none'
        })
      }
    })
  },

  // 修改昵称
  changeNickname() {
    wx.showModal({
      title: '修改昵称',
      editable: true,
      placeholderText: '请输入新昵称',
      success: (res) => {
        if (res.confirm && res.content) {
          const userInfo = { ...this.data.userInfo, nickName: res.content }
          this.updateUserInfo(userInfo)
        }
      }
    })
  },

  // 修改性别
  changeGender() {
    wx.showActionSheet({
      itemList: ['男', '女'],
      success: (res) => {
        const gender = res.tapIndex + 1
        const userInfo = { ...this.data.userInfo, gender }
        this.updateUserInfo(userInfo)
      }
    })
  },

  // 修改年龄
  changeAge() {
    wx.showModal({
      title: '修改年龄',
      editable: true,
      placeholderText: '请输入年龄',
      success: (res) => {
        if (res.confirm && res.content) {
          const age = parseInt(res.content)
          if (isNaN(age) || age < 0 || age > 150) {
            wx.showToast({
              title: '请输入有效年龄',
              icon: 'none'
            })
            return
          }
          const userInfo = { ...this.data.userInfo, age }
          this.updateUserInfo(userInfo)
        }
      }
    })
  },

  // 更新用户信息
  updateUserInfo(userInfo) {
    wx.setStorageSync('userInfo', userInfo)
    this.setData({ userInfo }, () => {
      wx.showToast({
        title: '修改成功',
        icon: 'success'
      })
    })
  }
})
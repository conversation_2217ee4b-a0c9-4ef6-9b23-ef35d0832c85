const express = require('express');
const router = express.Router();
const orderController = require('../controllers/orderController');
const auth = require('../middleware/auth');

// 创建订单
router.post('/', auth, orderController.createOrder);

// 获取订单列表
router.get('/', auth, orderController.getOrders);

// 获取订单详情
router.get('/:id', auth, orderController.getOrderDetail);

// 取消订单
router.put('/:id/cancel', auth, orderController.cancelOrder);

// 确认收货
router.put('/:id/confirm', auth, orderController.confirmOrder);

module.exports = router; 
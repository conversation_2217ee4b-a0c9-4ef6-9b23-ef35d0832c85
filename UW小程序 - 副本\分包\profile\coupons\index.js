// 分包/profile/coupons/index.js
import { api } from '../../../工具/api'
import { showToast, showLoading, hideLoading } from '../../../工具/util'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    tabs: ['可使用', '已使用', '已过期'],
    currentTab: 0,
    couponList: [],
    loading: false,
    pageNum: 1,
    pageSize: 10,
    hasMore: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadCoupons()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.setData({
      pageNum: 1,
      couponList: [],
      hasMore: true
    })
    
    this.loadCoupons()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (!this.data.hasMore || this.data.loading) return
    
    this.setData({
      pageNum: this.data.pageNum + 1
    })
    
    this.loadCoupons(true)
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  async loadCoupons(isLoadMore = false) {
    if (this.data.loading) return
    
    try {
      if (!isLoadMore) {
        showLoading()
      }
      
      this.setData({ loading: true })
      
      const res = await api.user.getCoupons({
        status: this.data.currentTab,
        pageNum: this.data.pageNum,
        pageSize: this.data.pageSize
      })
      
      if (res.success) {
        const newList = isLoadMore ? 
          [...this.data.couponList, ...res.data.list] : 
          res.data.list
          
        this.setData({
          couponList: newList,
          loading: false,
          hasMore: res.data.total > newList.length
        })
      }
    } catch (error) {
      console.error('获取优惠券列表失败：', error)
      showToast('获取优惠券列表失败')
      this.setData({ loading: false })
    } finally {
      if (!isLoadMore) {
        hideLoading()
      }
    }
  },

  switchTab(e) {
    const index = e.currentTarget.dataset.index
    if (index === this.data.currentTab) return
    
    this.setData({
      currentTab: index,
      pageNum: 1,
      couponList: [],
      hasMore: true
    })
    
    this.loadCoupons()
  },

  // 使用优惠券
  useCoupon(e) {
    const { id } = e.currentTarget.dataset
    // 跳转到商品列表页面
    wx.switchTab({
      url: '/页面/shop/shop'
    })
  }
})
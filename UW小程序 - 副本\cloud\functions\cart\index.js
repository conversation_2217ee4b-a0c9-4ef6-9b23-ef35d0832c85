// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('Received event:', event)
  
  const { action, data } = event
  const wxContext = cloud.getWXContext()
  const userId = wxContext.OPENID

  try {
    switch (action) {
      case 'add':
        return await addToCart(userId, data)
      case 'update':
        return await updateCart(userId, data)
      case 'remove':
        return await removeFromCart(userId, data)
      case 'list':
        return await getCartList(userId)
      default:
        return {
          success: false,
          message: '未知操作'
        }
    }
  } catch (error) {
    console.error('Error:', error)
    return {
      success: false,
      message: error.message
    }
  }
}

async function addToCart(userId, { productId, sku, quantity = 1 }) {
  const existItem = await db.collection('cart')
    .where({
      userId,
      productId,
      'sku.name': sku.name
    })
    .get()

  if (existItem.data.length > 0) {
    await db.collection('cart').doc(existItem.data[0]._id).update({
      data: {
        quantity: existItem.data[0].quantity + quantity
      }
    })
  } else {
    await db.collection('cart').add({
      data: {
        userId,
        productId,
        sku,
        quantity,
        createTime: db.serverDate()
      }
    })
  }

  return {
    success: true,
    message: '添加成功'
  }
}

async function updateCart(userId, { cartId, quantity }) {
  await db.collection('cart').doc(cartId).update({
    data: {
      quantity
    }
  })
  return {
    success: true,
    message: '更新成功'
  }
}

async function removeFromCart(userId, { cartId }) {
  await db.collection('cart').doc(cartId).remove()
  return {
    success: true,
    message: '删除成功'
  }
}

async function getCartList(userId) {
  const result = await db.collection('cart')
    .aggregate()
    .match({
      userId
    })
    .lookup({
      from: 'products',
      localField: 'productId',
      foreignField: '_id',
      as: 'product'
    })
    .end()

  return {
    success: true,
    data: result.list.map(item => ({
      ...item,
      product: item.product[0]
    }))
  }
} 
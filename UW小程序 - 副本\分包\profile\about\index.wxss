/* 分包/profile/about/index.wxss */
.container {
  min-height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 30rpx;
}

.company {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.version {
  font-size: 24rpx;
  color: #999;
}

.content {
  flex: 1;
  padding: 0 30rpx;
}

.section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.contact-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.contact-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: #333;
}

.value {
  font-size: 28rpx;
  color: #666;
}

.footer {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 24rpx;
}
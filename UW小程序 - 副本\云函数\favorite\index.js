// 云函数入口文件
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event;
  const { OPENID } = cloud.getWXContext();
  
  switch (action) {
    case 'add':
      return await addFavorite(OPENID, data);
    case 'remove':
      return await removeFavorite(OPENID, data);
    case 'check':
      return await checkFavorite(OPENID, data);
    default:
      throw new Error('未知的操作类型');
  }
};

// 添加收藏
async function addFavorite(openid, data) {
  const { productId } = data;
  
  try {
    // 检查是否已收藏
    const exists = await db.collection('favorites')
      .where({
        openid,
        productId
      })
      .count();
      
    if (exists.total > 0) {
      return {
        success: false,
        message: '已收藏过该商品'
      };
    }
    
    // 添加收藏
    await db.collection('favorites').add({
      data: {
        openid,
        productId,
        createdAt: db.serverDate()
      }
    });
    
    return {
      success: true,
      message: '收藏成功'
    };
  } catch (error) {
    return {
      success: false,
      message: error.message
    };
  }
}

// 取消收藏
async function removeFavorite(openid, data) {
  const { productId } = data;
  
  try {
    await db.collection('favorites')
      .where({
        openid,
        productId
      })
      .remove();
      
    return {
      success: true,
      message: '取消收藏成功'
    };
  } catch (error) {
    return {
      success: false,
      message: error.message
    };
  }
}

// 检查收藏状态
async function checkFavorite(openid, data) {
  const { productId } = data;
  
  try {
    const favorite = await db.collection('favorites')
      .where({
        openid,
        productId
      })
      .count();
      
    return {
      success: true,
      data: {
        isFavorite: favorite.total > 0
      }
    };
  } catch (error) {
    return {
      success: false,
      message: error.message
    };
  }
} 
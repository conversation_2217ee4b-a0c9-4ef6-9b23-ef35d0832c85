const mongoose = require('mongoose');

const orderSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  orderNo: {
    type: String,
    required: true,
    unique: true
  },
  products: [{
    product: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product'
    },
    sku: {
      name: String,
      price: Number
    },
    quantity: Number,
    price: Number
  }],
  totalAmount: {
    type: Number,
    required: true
  },
  address: {
    name: String,
    phone: String,
    province: String,
    city: String,
    district: String,
    address: String
  },
  status: {
    type: Number,
    default: 1
    // 0: 已取消
    // 1: 待付款
    // 2: 待发货
    // 3: 待收货
    // 4: 已完成
  },
  paymentMethod: String,
  paymentTime: Date,
  shipmentTime: Date,
  completionTime: Date,
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('Order', orderSchema); 
const express = require('express');
const router = express.Router();

// 导入各个路由模块
const productRoutes = require('./product');
const userRoutes = require('./user');
const orderRoutes = require('./order');
const cartRoutes = require('./cart');

// 注册路由
router.use('/products', productRoutes);
router.use('/user', userRoutes);
router.use('/orders', orderRoutes);
router.use('/cart', cartRoutes);

module.exports = router; 
/* subPackage/profile/userInfo/index.wxss */
.container {
  padding: 30rpx;
  background: #f8f8f8;
  min-height: 100vh;
}

.info-list {
  background: #fff;
  border-radius: 12rpx;
  padding: 0 30rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  color: #333;
  font-size: 28rpx;
}

.right-content {
  display: flex;
  align-items: center;
}

.value {
  color: #666;
  font-size: 28rpx;
  margin-right: 10rpx;
}

.value.disabled {
  color: #999;
}

.arrow {
  color: #999;
  font-size: 24rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}
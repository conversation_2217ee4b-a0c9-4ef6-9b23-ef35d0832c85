const { AppError } = require('./errorHandler');
const User = require('../models/User');

module.exports = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];
    
    if (!token) {
      throw new AppError('未登录', 401);
    }
    
    // TODO: 验证JWT token
    const openid = token;
    
    const user = await User.findOne({ openid });
    if (!user) {
      throw new AppError('用户不存在', 401);
    }
    
    req.userId = user._id;
    next();
  } catch (error) {
    next(error);
  }
}; 
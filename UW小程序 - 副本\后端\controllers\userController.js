const User = require('../models/User');
const { AppError } = require('../middleware/errorHandler');

exports.login = async (req, res) => {
  try {
    const { code } = req.body;
    
    // TODO: 调用微信登录接口获取openid
    const openid = 'test_openid';
    
    let user = await User.findOne({ openid });
    
    if (!user) {
      user = await User.create({ openid });
    }
    
    res.json({
      success: true,
      data: {
        token: openid, // TODO: 生成JWT token
        userInfo: user
      }
    });
  } catch (error) {
    throw new AppError(error.message);
  }
};

exports.getUserInfo = async (req, res) => {
  try {
    const { userId } = req;
    const user = await User.findById(userId);
    
    if (!user) {
      throw new AppError('用户不存在', 404);
    }
    
    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    throw new AppError(error.message);
  }
};

exports.updateUserInfo = async (req, res) => {
  try {
    const { userId } = req;
    const updateData = req.body;
    
    const user = await User.findByIdAndUpdate(
      userId,
      updateData,
      { new: true }
    );
    
    if (!user) {
      throw new AppError('用户不存在', 404);
    }
    
    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    throw new AppError(error.message);
  }
};

exports.getAddresses = async (req, res) => {
  try {
    const { userId } = req;
    const user = await User.findById(userId);
    
    if (!user) {
      throw new AppError('用户不存在', 404);
    }
    
    res.json({
      success: true,
      data: user.addresses
    });
  } catch (error) {
    throw new AppError(error.message);
  }
};

exports.addAddress = async (req, res) => {
  try {
    const { userId } = req;
    const addressData = req.body;
    
    const user = await User.findById(userId);
    
    if (!user) {
      throw new AppError('用户不存在', 404);
    }
    
    if (addressData.isDefault) {
      user.addresses.forEach(addr => {
        addr.isDefault = false;
      });
    }
    
    user.addresses.push(addressData);
    await user.save();
    
    res.json({
      success: true,
      data: user.addresses
    });
  } catch (error) {
    throw new AppError(error.message);
  }
};

exports.updateAddress = async (req, res) => {
  try {
    const { userId } = req;
    const { id } = req.params;
    const updateData = req.body;
    
    const user = await User.findById(userId);
    
    if (!user) {
      throw new AppError('用户不存在', 404);
    }
    
    const address = user.addresses.id(id);
    
    if (!address) {
      throw new AppError('地址不存在', 404);
    }
    
    if (updateData.isDefault) {
      user.addresses.forEach(addr => {
        addr.isDefault = false;
      });
    }
    
    Object.assign(address, updateData);
    await user.save();
    
    res.json({
      success: true,
      data: user.addresses
    });
  } catch (error) {
    throw new AppError(error.message);
  }
};

exports.deleteAddress = async (req, res) => {
  try {
    const { userId } = req;
    const { id } = req.params;
    
    const user = await User.findById(userId);
    
    if (!user) {
      throw new AppError('用户不存在', 404);
    }
    
    user.addresses.id(id).remove();
    await user.save();
    
    res.json({
      success: true,
      data: user.addresses
    });
  } catch (error) {
    throw new AppError(error.message);
  }
}; 
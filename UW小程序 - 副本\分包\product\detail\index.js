// 分包/product/detail/index.js
const { api } = require('../../../工具/api')
const { showToast, showLoading, hideLoading, showModal } = require('../../../工具/util')
const store = require('../../../状态管理/store')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    id: '',
    product: null,
    selectedSku: null,
    quantity: 1,
    comments: [],
    relatedProducts: [],
    loading: false,
    showSkuPopup: false,
    isFavorite: false  // 添加收藏状态
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { id } = options
    this.setData({ id })
    this.loadData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时重新检查收藏状态
    if (store.getState().userInfo) {
      this.checkFavoriteStatus()
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 加载所有数据
  async loadData() {
    if (this.data.loading) return
    
    try {
      showLoading()
      this.setData({ loading: true })
      
      const loadTasks = [
        this.loadProductDetail(),
        this.loadComments(),
        this.loadRelatedProducts()
      ]

      // 已登录才检查收藏状态
      if (store.getState().userInfo) {
        loadTasks.push(this.checkFavoriteStatus())
      }
      
      await Promise.all(loadTasks)
    } catch (error) {
      console.error('加载数据失败:', error)
      showToast('加载失败，请重试')
    } finally {
      hideLoading()
      this.setData({ loading: false })
    }
  },

  // 检查收藏状态
  async checkFavoriteStatus() {
    try {
      const res = await api.favorite.check(this.data.id)
      if (res.success) {
        this.setData({ isFavorite: res.data.isFavorite })
      }
    } catch (error) {
      console.error('检查收藏状态失败:', error)
    }
  },

  // 收藏/取消收藏
  async toggleFavorite() {
    // 检查登录状态
    if (!store.getState().userInfo) {
      const confirmed = await showModal({
        title: '提示',
        content: '请先登录后再收藏',
        confirmText: '去登录'
      })
      
      if (confirmed) {
        wx.navigateTo({
          url: '/页面/profile/profile'
        })
      }
      return
    }

    try {
      showLoading()
      const res = await (this.data.isFavorite ? 
        api.favorite.remove(this.data.id) : 
        api.favorite.add(this.data.id))
      
      if (res.success) {
        this.setData({ isFavorite: !this.data.isFavorite })
        showToast(this.data.isFavorite ? '收藏成功' : '已取消收藏', 'success')
      }
    } catch (error) {
      console.error('操作收藏失败:', error)
      showToast('操作失败，请重试')
    } finally {
      hideLoading()
    }
  },

  // 加载商品详情
  async loadProductDetail() {
    try {
      const res = await api.product.getProductDetail(this.data.id)
      if (res.success) {
        this.setData({
          product: res.data,
          selectedSku: res.data.skus[0] // 默认选择第一个SKU
        })
      }
    } catch (error) {
      console.error('加载商品详情失败:', error)
      throw error
    }
  },

  // 加载评论
  async loadComments() {
    try {
      const res = await api.product.getProductComments(this.data.id, { page: 1, limit: 5 })
      if (res.success) {
        this.setData({ comments: res.data.list })
      }
    } catch (error) {
      console.error('加载评论失败:', error)
      throw error
    }
  },

  // 加载相关商品
  async loadRelatedProducts() {
    try {
      const res = await api.product.getRelatedProducts(this.data.id)
      if (res.success) {
        this.setData({ relatedProducts: res.data })
      }
    } catch (error) {
      console.error('加载相关商品失败:', error)
      throw error
    }
  }
})
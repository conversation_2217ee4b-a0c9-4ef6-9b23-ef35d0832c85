const { api, CLOUD_PATHS } = require('../../工具/api.js');
const { showToast, showLoading, hideLoading } = require('../../工具/util.js');

data: {
  id: '',
  product: null,
  isFavorite: false,  // 是否已收藏
  loading: false,
  bannerList: [],
  productList: []
},

// 页面加载时执行
onLoad(options) {
  const { id } = options;
  this.setData({ id });
  this.loadData();
  this.loadBanners();
  this.loadProducts();
},

// 加载所有数据
async loadData() {
  if (this.data.loading) return;
  
  try {
    showLoading();
    this.setData({ loading: true });
    
    await Promise.all([
      this.loadProductDetail(),  // 加载商品详情
      this.checkFavoriteStatus()  // 检查收藏状态
    ]);
  } catch (error) {
    console.error('加载数据失败:', error);
    showToast('加载失败，请重试');
  } finally {
    hideLoading();
    this.setData({ loading: false });
  }
},

// 检查商品是否已收藏
async checkFavoriteStatus() {
  try {
    const res = await api.favorite.check(this.data.id);
    if (res.success) {
      this.setData({ isFavorite: res.data.isFavorite });
    }
  } catch (error) {
    console.error('检查收藏状态失败:', error);
  }
},

// 切换收藏状态
async toggleFavorite() {
  if (!store.getState().userInfo) {
    wx.navigateTo({ url: '/页面/profile/profile' });
    return;
  }

  try {
    showLoading();
    const res = await (this.data.isFavorite ? 
      api.favorite.remove(this.data.id) : 
      api.favorite.add(this.data.id));

    if (res.success) {
      this.setData({ isFavorite: !this.data.isFavorite });
      showToast(this.data.isFavorite ? '收藏成功' : '已取消收藏', 'success');
    } else {
      showToast(res.message);
    }
  } catch (error) {
    console.error('操作收藏失败:', error);
    showToast('操作失败，请重试');
  } finally {
    hideLoading();
  }
},

// 加载轮播图数据
async loadBanners() {
  try {
    const db = wx.cloud.database();
    const res = await db.collection('banners')
      .where({ status: 1 })
      .get();
    
    this.setData({ 
      bannerList: res.data.map(banner => ({
        ...banner,
        imageUrl: `${CLOUD_PATHS.BANNER}${banner.imageUrl}`
      }))
    });
  } catch (error) {
    console.error('加载轮播图失败:', error);
    showToast('加载轮播图失败');
  }
},

// 加载商品列表
async loadProducts() {
  if (this.data.loading) return;
  
  try {
    showLoading();
    this.setData({ loading: true });
    
    const res = await api.product.list({
      page: 1,
      limit: 10
    });
    
    if (res.success) {
      this.setData({ 
        productList: res.data.list,
        loading: false
      });
    }
  } catch (error) {
    console.error('加载商品列表失败:', error);
    showToast('加载商品列表失败');
    this.setData({ loading: false });
  } finally {
    hideLoading();
  }
},

// 点击商品
onProductTap(e) {
  const { id } = e.currentTarget.dataset;
  wx.navigateTo({
    url: `/分包/product/detail/index?id=${id}`
  });
},

// 下拉刷新
async onPullDownRefresh() {
  try {
    await Promise.all([
      this.loadBanners(),
      this.loadProducts()
    ]);
  } finally {
    wx.stopPullDownRefresh();
  }
} 
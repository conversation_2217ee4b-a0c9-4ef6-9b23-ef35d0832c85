<!--分包/profile/address/list.wxml-->
<view class="container">
  <!-- 地址列表 -->
  <scroll-view 
    class="address-list" 
    scroll-y 
    enable-back-to-top
  >
    <view 
      class="address-item" 
      wx:for="{{addressList}}" 
      wx:key="id"
      bindtap="{{from ? 'selectAddress' : ''}}"
      data-id="{{item.id}}"
    >
      <view class="address-info">
        <view class="user-info">
          <text class="name">{{item.name}}</text>
          <text class="phone">{{item.phone}}</text>
          <text class="tag" wx:if="{{item.isDefault}}">默认</text>
        </view>
        <view class="address-detail">{{item.province}}{{item.city}}{{item.district}}{{item.address}}</view>
      </view>

      <view class="address-actions" catchtap="stopPropagation">
        <view class="action-item" catchtap="setDefaultAddress" data-id="{{item.id}}" wx:if="{{!item.isDefault}}">
          <image class="action-icon" src="/图片/设置图标.png"></image>
          <text>设为默认</text>
        </view>
        <view class="action-item" catchtap="editAddress" data-id="{{item.id}}">
          <image class="action-icon" src="/图片/编辑.png"></image>
          <text>编辑</text>
        </view>
        <view class="action-item" catchtap="deleteAddress" data-id="{{item.id}}">
          <image class="action-icon" src="/图片/删除.png"></image>
          <text>删除</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && addressList.length === 0}}">
      <image class="empty-icon" src="/图片/地址.png"></image>
      <text class="empty-text">暂无收货地址</text>
    </view>
  </scroll-view>

  <!-- 底部按钮 -->
  <view class="bottom-btn">
    <button class="add-btn" bindtap="addAddress">+ 新增收货地址</button>
  </view>
</view>
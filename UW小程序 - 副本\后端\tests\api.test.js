const request = require('supertest');
const app = require('../server');
const mongoose = require('mongoose');

describe('API 测试', () => {
  beforeAll(async () => {
    // 连接测试数据库
    await mongoose.connect(process.env.TEST_MONGO_URI);
  });

  afterAll(async () => {
    // 断开数据库连接
    await mongoose.connection.close();
  });

  describe('用户认证', () => {
    test('用户登录 - 成功', async () => {
      const res = await request(app)
        .post('/api/login')
        .send({
          email: '<EMAIL>',
          password: 'Password123'
        });

      expect(res.status).toBe(200);
      expect(res.body).toHaveProperty('token');
    });

    test('用户登录 - 失败（密码错误）', async () => {
      const res = await request(app)
        .post('/api/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword'
        });

      expect(res.status).toBe(401);
    });
  });
}); 
<!--index.wxml-->
<view class="container">
  <!-- 轮播图 -->
  <swiper 
    class="banner" 
    autoplay 
    circular
    indicator-dots="{{true}}"
  >
    <swiper-item wx:for="{{bannerList}}" wx:key="id">
      <image src="{{item.imageUrl}}" mode="aspectFill"></image>
    </swiper-item>
  </swiper>

  <!-- 商品列表 -->
  <view class="product-list">
    <view 
      class="product-item" 
      wx:for="{{productList}}" 
      wx:key="id"
      bindtap="onProductTap"
      data-id="{{item.id}}"
    >
      <image class="product-image" src="{{item.imageUrl}}" mode="aspectFill"></image>
      <view class="product-info">
        <text class="product-name">{{item.name}}</text>
        <text class="product-price">¥{{item.price}}</text>
      </view>
    </view>
  </view>

  <!-- 加载中提示 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>
</view>

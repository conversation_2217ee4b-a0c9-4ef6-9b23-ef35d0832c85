<view class="container">
  <!-- 商品轮播图 -->
  <swiper class="product-swiper" indicator-dots circular>
    <swiper-item wx:for="{{product.images}}" wx:key="*this">
      <image src="{{item}}" mode="aspectFill"></image>
    </swiper-item>
  </swiper>

  <!-- 商品信息 -->
  <view class="product-info">
    <view class="product-title">{{product.name}}</view>
    <view class="product-price">¥{{selectedSku.price}}</view>
    <view class="product-desc">{{product.description}}</view>
  </view>

  <!-- SKU选择 -->
  <view class="sku-section" bindtap="showSkuPopup">
    <text class="section-title">规格</text>
    <view class="sku-info">
      <text>已选：{{selectedSku.name}}</text>
      <text class="arrow">></text>
    </view>
  </view>

  <!-- 商品评价 -->
  <view class="comment-section">
    <view class="section-header">
      <text class="section-title">商品评价</text>
      <text class="more">查看全部 ></text>
    </view>
    <view class="comment-list">
      <view class="comment-item" wx:for="{{comments}}" wx:key="id">
        <image class="user-avatar" src="{{item.userAvatar}}"></image>
        <view class="comment-content">
          <text class="user-name">{{item.userName}}</text>
          <text class="comment-text">{{item.content}}</text>
          <text class="comment-time">{{item.createTime}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 相关商品 -->
  <view class="related-section">
    <view class="section-header">
      <text class="section-title">相关商品</text>
    </view>
    <scroll-view class="related-list" scroll-x>
      <view 
        class="related-item" 
        wx:for="{{relatedProducts}}" 
        wx:key="id"
        bindtap="onProductTap"
        data-id="{{item.id}}"
      >
        <image src="{{item.imageUrl}}" mode="aspectFill"></image>
        <text class="related-name">{{item.name}}</text>
        <text class="related-price">¥{{item.price}}</text>
      </view>
    </scroll-view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar">
    <view class="action-buttons">
      <button class="add-cart" bindtap="addToCart">加入购物车</button>
      <button class="buy-now" bindtap="buyNow">立即购买</button>
    </view>
  </view>

  <!-- SKU选择弹窗 -->
  <view class="sku-popup {{showSkuPopup ? 'show' : ''}}">
    <view class="popup-mask" bindtap="hideSkuPopup"></view>
    <view class="popup-content">
      <view class="popup-header">
        <image class="sku-image" src="{{selectedSku.imageUrl}}" mode="aspectFill"></image>
        <view class="sku-info">
          <text class="sku-price">¥{{selectedSku.price}}</text>
          <text class="sku-stock">库存：{{selectedSku.stock}}</text>
        </view>
        <view class="close-btn" bindtap="hideSkuPopup">×</view>
      </view>

      <view class="sku-options">
        <text class="options-title">规格</text>
        <view class="options-list">
          <view 
            class="option-item {{item.id === selectedSku.id ? 'selected' : ''}}"
            wx:for="{{product.skus}}"
            wx:key="id"
            bindtap="onSelectSku"
            data-sku="{{item}}"
          >
            {{item.name}}
          </view>
        </view>
      </view>

      <view class="quantity-section">
        <text class="quantity-title">数量</text>
        <view class="quantity-control">
          <button 
            class="minus {{quantity <= 1 ? 'disabled' : ''}}"
            bindtap="onQuantityChange"
            data-type="minus"
          >-</button>
          <input 
            type="number" 
            value="{{quantity}}"
            bindchange="onQuantityChange"
          />
          <button 
            class="plus {{quantity >= selectedSku.stock ? 'disabled' : ''}}"
            bindtap="onQuantityChange"
            data-type="plus"
          >+</button>
        </view>
      </view>

      <view class="popup-buttons">
        <button class="add-cart" bindtap="addToCart">加入购物车</button>
        <button class="buy-now" bindtap="buyNow">立即购买</button>
      </view>
    </view>
  </view>
</view> 
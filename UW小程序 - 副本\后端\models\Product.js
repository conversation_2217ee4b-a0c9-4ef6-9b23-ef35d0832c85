const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  name: { 
    type: String, 
    required: true 
  },
  description: String,
  price: { 
    type: Number, 
    required: true 
  },
  originalPrice: {
    type: Number
  },
  images: [String],
  category: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Category' 
  },
  skus: [{
    name: String,
    price: Number,
    originalPrice: Number,
    stock: Number,
    imageUrl: String
  }],
  sales: {
    type: Number,
    default: 0
  },
  rating: {
    type: Number,
    default: 5
  },
  status: {
    type: Number,
    default: 1 // 1: 上架, 0: 下架
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('Product', productSchema); 
<view class="container">
  <!-- 商品列表 -->
  <view class="product-list">
    <block wx:if="{{!loading}}">
      <view class="product-item" 
            wx:for="{{productList}}" 
            wx:key="_id"
            bindtap="goToDetail"
            data-id="{{item._id}}">
        <image class="product-image" 
               src="{{item.images[0] || '/images/placeholder.png'}}" 
               mode="aspectFill"
               lazy-load/>
        <view class="product-info">
          <view class="product-name">{{item.name}}</view>
          <view class="product-price">¥{{item.price}}</view>
          <view class="product-desc">{{item.description}}</view>
        </view>
      </view>
    </block>
    <block wx:else>
      <!-- 骨架屏 -->
      <view class="product-item" wx:for="{{4}}" wx:key="index">
        <view class="product-image skeleton"></view>
        <view class="product-info">
          <view class="product-name skeleton" style="height: 32rpx; width: 80%;"></view>
          <view class="product-price skeleton" style="height: 40rpx; width: 40%; margin: 16rpx 0;"></view>
          <view class="product-desc skeleton" style="height: 28rpx; width: 60%;"></view>
        </view>
      </view>
    </block>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && productList.length === 0}}">
    <image src="/images/empty.png" mode="aspectFit"/>
    <text>暂无商品</text>
  </view>
</view> 
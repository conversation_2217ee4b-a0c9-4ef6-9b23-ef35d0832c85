const Cart = require('../models/Cart');
const Product = require('../models/Product');
const { AppError } = require('../middleware/errorHandler');

exports.getCartList = async (req, res) => {
  try {
    const { userId } = req;
    
    const cartItems = await Cart.find({ user: userId })
      .populate('product');
    
    res.json({
      success: true,
      data: cartItems
    });
  } catch (error) {
    throw new AppError(error.message);
  }
};

exports.addToCart = async (req, res) => {
  try {
    const { userId } = req;
    const { productId, skuId, quantity } = req.body;
    
    const product = await Product.findById(productId);
    if (!product) {
      throw new AppError('商品不存在', 404);
    }
    
    const sku = product.skus.find(s => s._id.toString() === skuId);
    if (!sku) {
      throw new AppError('商品规格不存在', 404);
    }
    
    if (sku.stock < quantity) {
      throw new AppError('商品库存不足', 400);
    }
    
    // 检查是否已存在相同商品和SKU
    let cartItem = await Cart.findOne({
      user: userId,
      product: productId,
      'sku.name': sku.name
    });
    
    if (cartItem) {
      // 更新数量
      cartItem.quantity += quantity;
      if (cartItem.quantity > sku.stock) {
        throw new AppError('商品库存不足', 400);
      }
      await cartItem.save();
    } else {
      // 创建新购物车项
      cartItem = await Cart.create({
        user: userId,
        product: productId,
        sku: {
          name: sku.name,
          price: sku.price,
          imageUrl: sku.imageUrl
        },
        quantity
      });
    }
    
    res.json({
      success: true,
      data: cartItem
    });
  } catch (error) {
    throw new AppError(error.message);
  }
};

exports.updateCartItem = async (req, res) => {
  try {
    const { userId } = req;
    const { id } = req.params;
    const { quantity, selected } = req.body;
    
    const cartItem = await Cart.findOne({
      _id: id,
      user: userId
    });
    
    if (!cartItem) {
      throw new AppError('购物车商品不存在', 404);
    }
    
    if (quantity !== undefined) {
      const product = await Product.findById(cartItem.product);
      if (!product) {
        throw new AppError('商品不存在', 404);
      }
      
      const sku = product.skus.find(s => s.name === cartItem.sku.name);
      if (!sku) {
        throw new AppError('商品规格不存在', 404);
      }
      
      if (sku.stock < quantity) {
        throw new AppError('商品库存不足', 400);
      }
      
      cartItem.quantity = quantity;
    }
    
    if (selected !== undefined) {
      cartItem.selected = selected;
    }
    
    await cartItem.save();
    
    res.json({
      success: true,
      data: cartItem
    });
  } catch (error) {
    throw new AppError(error.message);
  }
};

exports.removeFromCart = async (req, res) => {
  try {
    const { userId } = req;
    const { id } = req.params;
    
    const cartItem = await Cart.findOneAndDelete({
      _id: id,
      user: userId
    });
    
    if (!cartItem) {
      throw new AppError('购物车商品不存在', 404);
    }
    
    res.json({
      success: true,
      data: null
    });
  } catch (error) {
    throw new AppError(error.message);
  }
};

exports.clearCart = async (req, res) => {
  try {
    const { userId } = req;
    
    await Cart.deleteMany({ user: userId });
    
    res.json({
      success: true,
      data: null
    });
  } catch (error) {
    throw new AppError(error.message);
  }
}; 
<view class="container">
  <view class="info-list">
    <view class="info-item">
      <text class="label">头像</text>
      <image class="avatar" src="{{userInfo.avatarUrl}}"></image>
    </view>
    <view class="info-item">
      <text class="label">昵称</text>
      <text class="value">{{userInfo.nickName}}</text>
    </view>
    <view class="info-item">
      <text class="label">手机号</text>
      <text class="value">{{userInfo.phone || '未绑定'}}</text>
    </view>
    <view class="info-item">
      <text class="label">性别</text>
      <text class="value">{{userInfo.gender === 1 ? '男' : '女'}}</text>
    </view>
    <view class="info-item">
      <text class="label">年龄</text>
      <text class="value">{{userInfo.age || '未设置'}}</text>
    </view>
    <view class="info-item">
      <text class="label">会员等级</text>
      <text class="value">{{userInfo.level}}</text>
    </view>
  </view>
</view> 
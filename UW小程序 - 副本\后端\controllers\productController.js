const Product = require('../models/Product');
const { AppError } = require('../middleware/errorHandler');

exports.getProducts = async (req, res) => {
  try {
    const { page = 1, limit = 10, category, keyword } = req.query;
    const query = { status: 1 };
    
    if (category) {
      query.category = category;
    }
    
    if (keyword) {
      query.name = new RegExp(keyword, 'i');
    }
    
    const products = await Product.find(query)
      .skip((page - 1) * limit)
      .limit(parseInt(limit))
      .sort({ createdAt: -1 });
      
    const total = await Product.countDocuments(query);
    
    res.json({
      success: true,
      data: {
        list: products,
        total,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    throw new AppError(error.message);
  }
};

exports.getProductDetail = async (req, res) => {
  try {
    const { id } = req.params;
    const product = await Product.findById(id);
    
    if (!product) {
      throw new AppError('商品不存在', 404);
    }
    
    res.json({
      success: true,
      data: product
    });
  } catch (error) {
    throw new AppError(error.message);
  }
};

exports.getProductComments = async (req, res) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 10 } = req.query;
    
    // TODO: 实现评论功能
    const comments = [];
    const total = 0;
    
    res.json({
      success: true,
      data: {
        list: comments,
        total,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    throw new AppError(error.message);
  }
};

exports.getRelatedProducts = async (req, res) => {
  try {
    const { id } = req.params;
    const product = await Product.findById(id);
    
    if (!product) {
      throw new AppError('商品不存在', 404);
    }
    
    const relatedProducts = await Product.find({
      category: product.category,
      _id: { $ne: id },
      status: 1
    }).limit(6);
    
    res.json({
      success: true,
      data: relatedProducts
    });
  } catch (error) {
    throw new AppError(error.message);
  }
}; 
const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const couponController = require('../controllers/couponController');
const auth = require('../middleware/auth');

// 登录
router.post('/login', userController.login);

// 获取用户信息
router.get('/info', auth, userController.getUserInfo);

// 更新用户信息
router.put('/info', auth, userController.updateUserInfo);

// 地址管理
router.get('/addresses', auth, userController.getAddresses);
router.post('/addresses', auth, userController.addAddress);
router.put('/addresses/:id', auth, userController.updateAddress);
router.delete('/addresses/:id', auth, userController.deleteAddress);

// 优惠券管理
router.get('/coupons', auth, couponController.getCoupons);
router.post('/coupons/receive', auth, couponController.receiveCoupon);
router.put('/coupons/:id/use', auth, couponController.useCoupon);

module.exports = router; 
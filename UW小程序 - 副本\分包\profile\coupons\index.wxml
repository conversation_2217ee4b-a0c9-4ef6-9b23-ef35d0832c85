<!--分包/profile/coupons/index.wxml-->
<view class="container">
  <!-- 顶部标签页 -->
  <view class="tabs">
    <view 
      class="tab-item {{currentTab === index ? 'active' : ''}}" 
      wx:for="{{tabs}}" 
      wx:key="*this"
      data-index="{{index}}"
      bindtap="switchTab"
    >
      <text>{{item}}</text>
      <view class="tab-line" wx:if="{{currentTab === index}}"></view>
    </view>
  </view>

  <!-- 优惠券列表 -->
  <scroll-view 
    class="coupon-list" 
    scroll-y 
    enable-back-to-top
    bindscrolltolower="onReachBottom"
  >
    <view class="coupon-item {{item.status !== 0 ? 'disabled' : ''}}" 
      wx:for="{{couponList}}" 
      wx:key="id"
    >
      <view class="coupon-left">
        <view class="amount">
          <text class="symbol">¥</text>
          <text class="number">{{item.amount}}</text>
        </view>
        <view class="condition">满{{item.minAmount}}元可用</view>
      </view>

      <view class="coupon-right">
        <view class="coupon-info">
          <view class="coupon-name">{{item.name}}</view>
          <view class="coupon-time">{{item.startTime}} - {{item.endTime}}</view>
          <view class="coupon-desc">{{item.description}}</view>
        </view>
        <view class="coupon-action">
          <button 
            class="use-btn {{item.status !== 0 ? 'disabled' : ''}}" 
            bindtap="useCoupon"
            data-id="{{item.id}}"
            wx:if="{{item.status === 0}}"
          >立即使用</button>
          <text class="status-text" wx:else>{{item.status === 1 ? '已使用' : '已过期'}}</text>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-status">
      <view class="loading" wx:if="{{loading}}">加载中...</view>
      <view class="no-more" wx:if="{{!loading && !hasMore}}">没有更多优惠券了</view>
    </view>
  </scroll-view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && couponList.length === 0}}">
    <image class="empty-icon" src="/图片/优惠券.png"></image>
    <text class="empty-text">暂无相关优惠券</text>
  </view>
</view>
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-info">
    <image class="avatar" src="{{userInfo.avatarUrl || '/图片/未登录头像.png'}}"></image>
    <view class="info">
      <text class="nickname">{{userInfo.nickName || '未登录'}}</text>
      <text class="level" wx:if="{{userInfo}}">{{userInfo.level}}</text>
    </view>
    <button 
      class="login-btn" 
      wx:if="{{!isLogin}}"
      open-type="getPhoneNumber" 
      bindgetphonenumber="getPhoneNumber"
    >
      立即登录
    </button>
  </view>

  <!-- 订单区域 -->
  <view class="order-section">
    <view class="section-header" bindtap="viewAllOrders">
      <text class="title">我的订单</text>
      <text class="more">查看全部 ></text>
    </view>
    <view class="order-types">
      <view class="type-item" bindtap="goToOrders" data-type="1">
        <image src="/图片/待付款.png"></image>
        <text>待付款</text>
      </view>
      <view class="type-item" bindtap="goToOrders" data-type="2">
        <image src="/图片/待发货.png"></image>
        <text>待发货</text>
      </view>
      <view class="type-item" bindtap="goToOrders" data-type="3">
        <image src="/图片/待收货.png"></image>
        <text>待收货</text>
      </view>
      <view class="type-item" bindtap="goToOrders" data-type="4">
        <image src="/图片/已完成.png"></image>
        <text>已完成</text>
      </view>
    </view>
  </view>

  <!-- 功能列表 -->
  <view class="function-list">
    <view class="function-item" bindtap="goToFavorites">
      <image class="icon" src="/图片/收藏.png"></image>
      <text class="name">我的收藏</text>
      <image class="arrow" src="/图片/箭头.png"></image>
    </view>
    <view class="function-item" bindtap="goToCoupons">
      <image class="icon" src="/图片/优惠券.png"></image>
      <text class="name">优惠券</text>
      <image class="arrow" src="/图片/箭头.png"></image>
    </view>
    <view class="function-item" bindtap="goToAddress">
      <image class="icon" src="/图片/地址.png"></image>
      <text class="name">收货地址</text>
      <image class="arrow" src="/图片/箭头.png"></image>
    </view>
  </view>

  <!-- 其他功能 -->
  <view class="other-list">
    <view class="function-item" bindtap="contactService">
      <image class="icon" src="/图片/客服.png"></image>
      <text class="name">联系客服</text>
      <image class="arrow" src="/图片/箭头.png"></image>
    </view>
    <view class="function-item" bindtap="goToAgreement">
      <image class="icon" src="/图片/协议.png"></image>
      <text class="name">用户协议</text>
      <image class="arrow" src="/图片/箭头.png"></image>
    </view>
    <view class="function-item" bindtap="goToAbout">
      <image class="icon" src="/图片/关于.png"></image>
      <text class="name">关于我们</text>
      <image class="arrow" src="/图片/箭头.png"></image>
    </view>
  </view>
</view> 
const config = require('../config');
const logger = require('../utils/logger');

class AppError extends Error {
  constructor(message, status = 500, details = null) {
    super(message);
    this.status = status;
    this.details = details;
  }
}

const errorHandler = (err, req, res, next) => {
  // 如果错误已经发送了响应，直接返回
  if (res.headersSent) {
    return next(err);
  }

  // 转换错误为AppError
  const error = err instanceof AppError ? err : new AppError(err.message);

  // 记录错误日志
  logger.error('错误:', {
    message: error.message,
    stack: error.stack,
    details: error.details,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip
  });

  // 准备错误响应
  const response = {
    success: false,
    message: error.message
  };

  // 在开发环境下添加更多信息
  if (config.nodeEnv === 'development') {
    response.stack = error.stack;
    response.details = error.details;
  }

  // 发送错误响应
  res.status(error.status).json(response);
};

module.exports = {
  AppError,
  errorHandler
}; 
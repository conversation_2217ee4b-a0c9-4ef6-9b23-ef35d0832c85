<view class="container">
  <!-- 收藏列表 -->
  <view class="favorite-list">
    <block wx:if="{{favoriteList.length > 0}}">
      <view class="favorite-item" wx:for="{{favoriteList}}" wx:key="id">
        <view class="product-info" bindtap="goToDetail" data-id="{{item.productId}}">
          <image class="product-image" src="{{item.product.imageUrl}}" mode="aspectFill" />
          <view class="product-detail">
            <view class="product-name">{{item.product.name}}</view>
            <view class="product-price">¥{{item.product.price}}</view>
          </view>
        </view>
        <view class="action-btn" bindtap="cancelFavorite" data-id="{{item.id}}">
          取消收藏
        </view>
      </view>
    </block>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{!loading && favoriteList.length === 0}}">
      <image class="empty-icon" src="/static/images/empty-favorite.png" mode="aspectFit" />
      <view class="empty-text">暂无收藏商品</view>
    </view>
    
    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading && !favoriteList.length}}">
      <view class="loading"></view>
      <view class="loading-text">加载中...</view>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{favoriteList.length > 0}}">
      <view wx:if="{{loading}}" class="loading-text">加载中...</view>
      <view wx:elif="{{!hasMore}}" class="no-more">没有更多了</view>
    </view>
  </view>
</view> 
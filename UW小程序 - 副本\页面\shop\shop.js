import { api } from '../../工具/api'
import { showToast, showLoading, hideLoading } from '../../工具/util'

Page({
  data: {
    currentTab: 0,
    productList: [],
    loading: true,
    categories: [],
    pageNum: 1,
    pageSize: 10,
    hasMore: true
  },

  onLoad() {
    this.loadCategories()
    this.loadProducts()
  },

  // 加载分类
  async loadCategories() {
    try {
      const res = await api.product.getCategories()
      if (res.success) {
        this.setData({ categories: res.data })
      }
    } catch (error) {
      console.error('获取分类失败：', error)
      showToast('获取分类失败')
    }
  },

  async loadProducts(isLoadMore = false) {
    if (this.data.loading) return
    
    try {
      if (!isLoadMore) {
        showLoading()
      }
      
      this.setData({ loading: true })
      
      const res = await api.product.list({
        categoryId: this.data.currentTab || '',
        pageNum: this.data.pageNum,
        pageSize: this.data.pageSize
      })
      
      if (res.success) {
        const newList = isLoadMore ? 
          [...this.data.productList, ...res.data.list] : 
          res.data.list
          
        this.setData({
          productList: newList,
          loading: false,
          hasMore: res.data.total > newList.length
        })
      }
    } catch (error) {
      console.error('获取商品列表失败：', error)
      showToast('获取商品列表失败')
      this.setData({ loading: false })
    } finally {
      if (!isLoadMore) {
        hideLoading()
      }
    }
  },

  // 切换分类
  async switchTab(e) {
    const index = e.currentTarget.dataset.index
    if (index === this.data.currentTab) return
    
    this.setData({ 
      currentTab: index,
      pageNum: 1,
      productList: [],
      hasMore: true
    })
    
    await this.loadProducts()
  },

  // 下拉刷新
  async onPullDownRefresh() {
    this.setData({
      pageNum: 1,
      productList: [],
      hasMore: true
    })
    
    await this.loadProducts()
    wx.stopPullDownRefresh()
  },

  // 上拉加载更多
  async onReachBottom() {
    if (!this.data.hasMore || this.data.loading) return
    
    this.setData({
      pageNum: this.data.pageNum + 1
    })
    
    await this.loadProducts(true)
  },

  // 跳转到商品详情
  goToDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/分包/product/detail/index?id=${id}`
    })
  }
}) 
const fs = require('fs');
const path = require('path');

// 定义目录结构
const directories = [
  // 前端目录
  '前端',
  '前端/src',
  '前端/public',
  '前端/assets',
  '前端/assets/images',
  '前端/assets/fonts',
  
  // 后端目录
  '后端',
  '后端/server',
  '后端/routes',
  '后端/controllers',
  '后端/models',
  
  // 工具目录
  '工具',
  '工具/scripts',
  '工具/config',
  
  // 数据库目录
  '数据库',
  '数据库/migrations',
  '数据库/seeds',
  
  // 测试目录
  '测试',
  '测试/unit',
  '测试/integration',
  
  // 文档目录
  '文档',
  '文档/api',
  '文档/guide',
  
  // 其他目录
  '其他',
  '其他/logs',
  '其他/temp'
];

// 创建目录函数
function createDirectories() {
  directories.forEach(dir => {
    const dirPath = path.join(process.cwd(), dir);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      console.log(`创建目录: ${dir}`);
    }
  });
}

// 在每个主目录下创建说明文件
function createReadmeFiles() {
  const mainDirs = ['前端', '后端', '工具', '数据库', '测试', '文档', '其他'];
  
  const readmeContent = {
    '前端': '存放前端相关代码和资源\n- src: 源代码\n- public: 公共资源\n- assets: 静态资源',
    '后端': '存放后端相关代码\n- server: 服务器代码\n- routes: 路由\n- controllers: 控制器\n- models: 数据模型',
    '工具': '存放工具和配置文件\n- scripts: 脚本文件\n- config: 配置文件',
    '数据库': '存放数据库相关文件\n- migrations: 数据库迁移\n- seeds: 数据库种子文件',
    '测试': '存放测试相关文件\n- unit: 单元测试\n- integration: 集成测试',
    '文档': '存放项目文档\n- api: API文档\n- guide: 使用指南',
    '其他': '存放其他文件\n- logs: 日志文件\n- temp: 临时文件'
  };

  mainDirs.forEach(dir => {
    const readmePath = path.join(process.cwd(), dir, 'README.md');
    if (!fs.existsSync(readmePath)) {
      fs.writeFileSync(readmePath, readmeContent[dir], 'utf8');
      console.log(`创建说明文件: ${dir}/README.md`);
    }
  });
}

// 执行创建
createDirectories();
createReadmeFiles(); 
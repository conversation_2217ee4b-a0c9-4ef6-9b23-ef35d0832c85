class Store {
  constructor() {
    this._state = {
      userInfo: null,
      cartCount: 0,
      token: wx.getStorageSync('token') || null
    };
    this._listeners = new Set();
  }

  // 获取状态
  getState() {
    return { ...this._state };
  }

  // 设置状态
  setState(newState) {
    this._state = {
      ...this._state,
      ...newState
    };
    this._notify();
  }

  // 订阅状态变化
  subscribe(listener) {
    this._listeners.add(listener);
    return () => this._listeners.delete(listener);
  }

  // 通知所有订阅者
  _notify() {
    for (const listener of this._listeners) {
      listener(this._state);
    }
  }

  // 用户相关方法
  setUserInfo(userInfo) {
    this.setState({ userInfo });
  }

  clearUserInfo() {
    this.setState({ userInfo: null, token: null });
    wx.removeStorageSync('token');
  }

  // 购物车相关方法
  setCartCount(count) {
    this.setState({ cartCount: count });
  }

  // Token相关方法
  setToken(token) {
    wx.setStorageSync('token', token);
    this.setState({ token });
  }
}

const store = new Store();

module.exports = store; 
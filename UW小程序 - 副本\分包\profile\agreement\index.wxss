/* 分包/profile/agreement/index.wxss */
.container {
  min-height: 100vh;
  background: #fff;
  display: flex;
  flex-direction: column;
}

.content {
  padding: 30rpx;
}

.title {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
}

.agreement-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 加载状态样式 */
.loading-state {
  text-align: center;
  padding: 30rpx 0;
}

.loading {
  font-size: 24rpx;
  color: #999;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
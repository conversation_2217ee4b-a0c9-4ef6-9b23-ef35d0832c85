{"name": "uw-miniprogram-backend", "version": "1.0.0", "description": "UW小程序后端服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "lint": "eslint .", "test": "jest"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "winston": "^3.11.0"}, "devDependencies": {"eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}}
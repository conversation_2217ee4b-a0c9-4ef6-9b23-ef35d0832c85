const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  openid: {
    type: String,
    required: true,
    unique: true
  },
  nickName: String,
  avatarUrl: String,
  gender: Number,
  phone: String,
  level: {
    type: String,
    default: 'Lv1'
  },
  points: {
    type: Number,
    default: 0
  },
  addresses: [{
    name: String,
    phone: String,
    province: String,
    city: String,
    district: String,
    address: String,
    isDefault: {
      type: Boolean,
      default: false
    }
  }],
  favorites: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product'
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

module.exports = mongoose.model('User', userSchema); 
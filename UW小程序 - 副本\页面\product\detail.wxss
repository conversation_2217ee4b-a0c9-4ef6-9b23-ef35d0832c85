/* pages/product/detail.wxss */ 
.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 100rpx;
}

.product-swiper {
  width: 100%;
  height: 750rpx;
}

.product-swiper image {
  width: 100%;
  height: 100%;
}

.product-info {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.product-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.product-price {
  font-size: 40rpx;
  color: #ff4444;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.product-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.sku-section {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-title {
  font-size: 28rpx;
  color: #333;
}

.sku-info {
  font-size: 28rpx;
  color: #666;
}

.arrow {
  margin-left: 10rpx;
  color: #999;
}

.comment-section {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.more {
  font-size: 24rpx;
  color: #999;
}

.comment-list {
  margin-top: 20rpx;
}

.comment-item {
  display: flex;
  margin-bottom: 30rpx;
}

.user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.comment-content {
  flex: 1;
}

.user-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.comment-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 10rpx;
  display: block;
}

.comment-time {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.related-section {
  background: #fff;
  padding: 30rpx;
}

.related-list {
  white-space: nowrap;
  margin-top: 20rpx;
}

.related-item {
  display: inline-block;
  width: 200rpx;
  margin-right: 20rpx;
}

.related-item image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
}

.related-name {
  font-size: 24rpx;
  color: #333;
  margin-top: 10rpx;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.related-price {
  font-size: 28rpx;
  color: #ff4444;
  font-weight: bold;
  margin-top: 6rpx;
  display: block;
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #fff;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.action-buttons {
  display: flex;
  flex: 1;
}

.add-cart, .buy-now {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin: 0 10rpx;
}

.add-cart {
  background: #fff;
  color: #ff4444;
  border: 2rpx solid #ff4444;
}

.buy-now {
  background: #ff4444;
  color: #fff;
}

.sku-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  visibility: hidden;
}

.sku-popup.show {
  visibility: visible;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.popup-content {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx;
  transform: translateY(100%);
  transition: transform 0.3s ease-out;
}

.sku-popup.show .popup-content {
  transform: translateY(0);
}

.popup-header {
  display: flex;
  margin-bottom: 30rpx;
}

.sku-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.sku-info {
  flex: 1;
}

.sku-price {
  font-size: 36rpx;
  color: #ff4444;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.sku-stock {
  font-size: 24rpx;
  color: #999;
}

.close-btn {
  font-size: 48rpx;
  color: #999;
  padding: 10rpx;
}

.options-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.options-list {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 30rpx;
}

.option-item {
  padding: 10rpx 30rpx;
  border: 2rpx solid #ddd;
  border-radius: 30rpx;
  margin: 0 20rpx 20rpx 0;
  font-size: 24rpx;
  color: #333;
}

.option-item.selected {
  background: #ff4444;
  color: #fff;
  border-color: #ff4444;
}

.quantity-section {
  margin-bottom: 30rpx;
}

.quantity-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.quantity-control {
  display: flex;
  align-items: center;
}

.quantity-control button {
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border: 2rpx solid #ddd;
  background: #fff;
  font-size: 28rpx;
  color: #333;
  padding: 0;
}

.quantity-control button.disabled {
  color: #999;
  background: #f5f5f5;
}

.quantity-control input {
  width: 100rpx;
  height: 60rpx;
  text-align: center;
  font-size: 28rpx;
  margin: 0 20rpx;
  border: 2rpx solid #ddd;
}

.popup-buttons {
  display: flex;
  margin-top: 30rpx;
}

.popup-buttons button {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin: 0 10rpx;
} 
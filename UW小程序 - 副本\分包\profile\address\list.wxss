/* 分包/profile/address/list.wxss */
.container {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

/* 地址列表样式 */
.address-list {
  flex: 1;
  padding: 20rpx;
}

.address-item {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.address-info {
  border-bottom: 1rpx solid #f5f5f5;
  padding-bottom: 20rpx;
  margin-bottom: 20rpx;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.name {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-right: 20rpx;
}

.phone {
  font-size: 28rpx;
  color: #666;
}

.tag {
  font-size: 20rpx;
  color: #ff4444;
  border: 1rpx solid #ff4444;
  padding: 2rpx 10rpx;
  border-radius: 16rpx;
  margin-left: 20rpx;
}

.address-detail {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}

.address-actions {
  display: flex;
  justify-content: flex-end;
}

.action-item {
  display: flex;
  align-items: center;
  margin-left: 30rpx;
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.action-item text {
  font-size: 26rpx;
  color: #666;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部按钮样式 */
.bottom-btn {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.add-btn {
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: #000;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
}
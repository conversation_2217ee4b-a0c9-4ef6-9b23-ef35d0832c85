/* pages/profile/profile.wxss */
.container {
  min-height: 100vh;
  background: #f5f5f5;
}

.user-info {
  position: relative;
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx;
  background: #fff;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
}

.info {
  flex: 1;
}

.nickname {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.level {
  font-size: 24rpx;
  color: #ff4d4f;
  background: #fff0f0;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.login-btn {
  width: 160rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  background: #ff4d4f;
  color: #fff;
  font-size: 26rpx;
  border-radius: 30rpx;
  margin: 0;
  padding: 0;
}

.order-section {
  margin-top: 20rpx;
  background: #fff;
  padding: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.more {
  font-size: 26rpx;
  color: #999;
}

.order-types {
  display: flex;
  justify-content: space-between;
}

.type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.type-item image {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 10rpx;
}

.type-item text {
  font-size: 24rpx;
  color: #666;
}

.function-list, .other-list {
  margin-top: 20rpx;
  background: #fff;
}

.function-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.function-item:last-child {
  border-bottom: none;
}

.icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.name {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.arrow {
  width: 32rpx;
  height: 32rpx;
}

.vip-tip {
  font-size: 24rpx;
  color: #999;
  background: rgba(255, 255, 255, 0.8);
  padding: 4rpx 20rpx;
  border-radius: 20rpx;
  position: relative;
  z-index: 2;
}

.phone-auth-btn {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 999;
}

.full-area-btn {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
  opacity: 0;
  z-index: 3;
  margin: 0;
  padding: 0;
  border: none;
  background: none;
}

.full-area-btn::after {
  display: none;
}
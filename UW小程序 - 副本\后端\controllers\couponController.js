const Coupon = require('../models/Coupon');
const UserCoupon = require('../models/UserCoupon');
const { AppError } = require('../middleware/errorHandler');

exports.getCoupons = async (req, res) => {
  try {
    const { userId } = req;
    const { status = 0, page = 1, limit = 10 } = req.query;
    
    const now = new Date();
    let query = { user: userId };
    
    // 根据状态筛选
    switch (parseInt(status)) {
      case 0: // 未使用
        query.status = 0;
        query['coupon.endTime'] = { $gt: now };
        break;
      case 1: // 已使用
        query.status = 1;
        break;
      case 2: // 已过期
        query = {
          $or: [
            { status: 0, 'coupon.endTime': { $lte: now } },
            { status: 2 }
          ]
        };
        break;
    }
    
    const userCoupons = await UserCoupon.find(query)
      .populate('coupon')
      .skip((page - 1) * limit)
      .limit(parseInt(limit))
      .sort({ createdAt: -1 });
      
    const total = await UserCoupon.countDocuments(query);
    
    res.json({
      success: true,
      data: {
        list: userCoupons,
        total,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    throw new AppError(error.message);
  }
};

exports.receiveCoupon = async (req, res) => {
  try {
    const { userId } = req;
    const { couponId } = req.body;
    
    const coupon = await Coupon.findById(couponId);
    if (!coupon) {
      throw new AppError('优惠券不存在', 404);
    }
    
    if (coupon.status !== 1) {
      throw new AppError('优惠券已禁用', 400);
    }
    
    const now = new Date();
    if (now < coupon.startTime || now > coupon.endTime) {
      throw new AppError('优惠券不在有效期内', 400);
    }
    
    // 检查是否已领取
    const exists = await UserCoupon.findOne({
      user: userId,
      coupon: couponId
    });
    
    if (exists) {
      throw new AppError('已领取过该优惠券', 400);
    }
    
    const userCoupon = await UserCoupon.create({
      user: userId,
      coupon: couponId,
      status: 0
    });
    
    res.json({
      success: true,
      data: userCoupon
    });
  } catch (error) {
    throw new AppError(error.message);
  }
};

exports.useCoupon = async (req, res) => {
  try {
    const { userId } = req;
    const { id } = req.params;
    
    const userCoupon = await UserCoupon.findOne({
      _id: id,
      user: userId,
      status: 0
    }).populate('coupon');
    
    if (!userCoupon) {
      throw new AppError('优惠券不存在或无法使用', 404);
    }
    
    const now = new Date();
    if (now > userCoupon.coupon.endTime) {
      userCoupon.status = 2;
      await userCoupon.save();
      throw new AppError('优惠券已过期', 400);
    }
    
    userCoupon.status = 1;
    userCoupon.usedTime = now;
    await userCoupon.save();
    
    res.json({
      success: true,
      data: userCoupon
    });
  } catch (error) {
    throw new AppError(error.message);
  }
}; 
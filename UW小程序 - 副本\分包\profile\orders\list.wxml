<!--分包/profile/orders/list.wxml-->
<view class="container">
  <!-- 顶部标签页 -->
  <view class="tabs">
    <view 
      class="tab-item {{currentTab === index ? 'active' : ''}}" 
      wx:for="{{tabs}}" 
      wx:key="*this"
      data-index="{{index}}"
      bindtap="switchTab"
    >
      <text>{{item}}</text>
      <view class="tab-line" wx:if="{{currentTab === index}}"></view>
    </view>
  </view>

  <!-- 订单列表 -->
  <scroll-view 
    class="order-list" 
    scroll-y 
    enable-back-to-top
    bindscrolltolower="onReachBottom"
  >
    <view class="order-item" wx:for="{{orderList}}" wx:key="id">
      <view class="order-header">
        <text class="order-no">订单号：{{item.orderNo}}</text>
        <text class="order-status">{{tabs[item.status]}}</text>
      </view>

      <view 
        class="order-content"
        bindtap="viewOrderDetail"
        data-id="{{item.id}}"
      >
        <view class="product-list">
          <view class="product-item" wx:for="{{item.products}}" wx:key="id" wx:for-item="product">
            <image class="product-image" src="{{product.imageUrl}}" mode="aspectFill"></image>
            <view class="product-info">
              <view class="product-name">{{product.name}}</view>
              <view class="product-sku">{{product.skuName}}</view>
              <view class="product-price-count">
                <text class="product-price">¥{{product.price}}</text>
                <text class="product-count">x{{product.count}}</text>
              </view>
            </view>
          </view>
        </view>

        <view class="order-summary">
          <text class="total-count">共{{item.totalCount}}件商品</text>
          <text class="total-amount">实付款：¥{{item.totalAmount}}</text>
        </view>
      </view>

      <view class="order-footer">
        <view class="order-time">{{item.createTime}}</view>
        <view class="order-actions">
          <button 
            class="action-btn cancel" 
            wx:if="{{item.status === 1}}"
            bindtap="cancelOrder"
            data-id="{{item.id}}"
          >取消订单</button>
          <button 
            class="action-btn primary" 
            wx:if="{{item.status === 1}}"
          >立即付款</button>
          <button 
            class="action-btn primary" 
            wx:if="{{item.status === 3}}"
            bindtap="confirmOrder"
            data-id="{{item.id}}"
          >确认收货</button>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-status">
      <view class="loading" wx:if="{{loading}}">加载中...</view>
      <view class="no-more" wx:if="{{!loading && !hasMore}}">没有更多订单了</view>
    </view>
  </scroll-view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && orderList.length === 0}}">
    <image class="empty-icon" src="/图片/订单图标-未选中.png"></image>
    <text class="empty-text">暂无相关订单</text>
  </view>
</view>
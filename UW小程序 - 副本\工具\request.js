const BASE_URL = 'http://localhost:3000/api'; // 开发环境
// const BASE_URL = 'https://your-domain.com/api'; // 生产环境

class RequestError extends Error {
  constructor(message, code, data) {
    super(message);
    this.code = code;
    this.data = data;
  }
}

const request = async (options) => {
  const { url, method = 'GET', data, header = {} } = options;

  // 添加通用header
  const headers = {
    'Content-Type': 'application/json',
    ...header
  };

  try {
    const response = await new Promise((resolve, reject) => {
      wx.request({
        url: `${BASE_URL}${url}`,
        method,
        data,
        header: headers,
        success: resolve,
        fail: reject
      });
    });

    // 处理请求结果
    if (response.statusCode >= 200 && response.statusCode < 300) {
      return response.data;
    }

    // 处理错误响应
    throw new RequestError(
      response.data.message || '请求失败',
      response.statusCode,
      response.data
    );
  } catch (error) {
    // 网络错误处理
    if (!error.code) {
      throw new RequestError('网络连接失败，请检查网络设置', -1);
    }
    throw error;
  }
};

// 封装常用请求方法
const http = {
  get: (url, params) => request({ url, method: 'GET', data: params }),
  post: (url, data) => request({ url, method: 'POST', data }),
  put: (url, data) => request({ url, method: 'PUT', data }),
  delete: (url) => request({ url, method: 'DELETE' })
};

module.exports = {
  http,
  RequestError
}; 
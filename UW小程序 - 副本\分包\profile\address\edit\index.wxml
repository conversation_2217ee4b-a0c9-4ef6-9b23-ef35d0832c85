<view class="container">
  <view class="form">
    <view class="form-item">
      <text class="label">收货人</text>
      <input 
        class="input" 
        placeholder="请输入收货人姓名" 
        value="{{name}}"
        data-field="name"
        bindinput="onInput"
      />
    </view>

    <view class="form-item">
      <text class="label">手机号码</text>
      <input 
        class="input" 
        type="number"
        maxlength="11"
        placeholder="请输入手机号码" 
        value="{{phone}}"
        data-field="phone"
        bindinput="onInput"
      />
    </view>

    <view class="form-item">
      <text class="label">所在地区</text>
      <picker 
        mode="region" 
        value="{{region}}" 
        bindchange="bindRegionChange"
      >
        <view class="picker {{region[0] === '请选择' ? 'placeholder' : ''}}">
          {{region[0]}} {{region[1]}} {{region[2]}}
        </view>
      </picker>
    </view>

    <view class="form-item">
      <text class="label">详细地址</text>
      <textarea 
        class="textarea" 
        placeholder="请输入详细地址，如街道、门牌号等" 
        value="{{address}}"
        data-field="address"
        bindinput="onInput"
      ></textarea>
    </view>

    <view class="form-item switch-item">
      <text class="label">设为默认地址</text>
      <switch 
        checked="{{isDefault}}" 
        bindchange="switchChange"
        color="#000000"
      />
    </view>
  </view>

  <view class="bottom-btn">
    <button class="save-btn" bindtap="saveAddress">保存</button>
  </view>
</view> 
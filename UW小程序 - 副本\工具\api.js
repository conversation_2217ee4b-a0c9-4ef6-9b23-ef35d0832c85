const BASE_URL = 'https://api.yourserver.com';
const { http } = require('./request');

// 统一的请求处理
const request = (url, options = {}) => {
  const token = wx.getStorageSync('token');
  
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${BASE_URL}${url}`,
      ...options,
      header: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
        ...options.header
      },
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else {
          reject(res);
        }
      },
      fail: reject
    });
  });
};

// 错误处理
const handleError = (res) => {
  let message = '请求失败';
  
  switch(res.statusCode) {
    case 401:
      message = '未授权，请重新登录';
      // 清除登录状态
      wx.removeStorageSync('token');
      wx.removeStorageSync('userInfo');
      break;
    case 403:
      message = '拒绝访问';
      break;
    case 404:
      message = '请求地址不存在';
      break;
    case 500:
      message = '服务器内部错误';
      break;
    default:
      message = res.data?.message || '请求失败';
  }
  
  wx.showToast({
    title: message,
    icon: 'none'
  });
};

const callCloud = async (name, data) => {
  try {
    const res = await wx.cloud.callFunction({
      name,
      data
    })
    return res.result
  } catch (error) {
    console.error(`调用云函数 ${name} 失败:`, error)
    throw error
  }
}

// API 接口分类管理
export const api = {
  auth: {
    login: (data) => request('/api/auth/login', {
      method: 'POST',
      data
    })
  },

  // 商品相关
  product: {
    get: (id) => callCloud('product', {
      action: 'get',
      data: { id }
    }),
    list: (params = {}) => callCloud('product', {
      action: 'list',
      data: { limit: 10, ...params }
    }),
    getProducts: (params) => http.get('/products', params),
    getProductDetail: (id) => http.get(`/products/${id}`),
    getProductComments: (productId, params) => http.get(`/products/${productId}/comments`, params),
    getRelatedProducts: (productId) => http.get(`/products/${productId}/related`)
  },

  // 收藏相关
  favorite: {
    add: (productId) => callCloud('favorite', {
      action: 'add',
      data: { productId }
    }),
    remove: (productId) => callCloud('favorite', {
      action: 'remove',
      data: { productId }
    }),
    check: (productId) => callCloud('favorite', {
      action: 'check',
      data: { productId }
    })
  },

  // 购物车相关
  cart: {
    add: (data) => callCloud('cart', {
      action: 'add',
      data
    }),
    update: (data) => callCloud('cart', {
      action: 'update',
      data
    }),
    remove: (data) => callCloud('cart', {
      action: 'remove',
      data
    }),
    list: () => callCloud('cart', {
      action: 'list'
    }),
    getCartList: () => http.get('/cart'),
    addToCart: (data) => http.post('/cart', data),
    updateCartItem: (id, data) => http.put(`/cart/${id}`, data),
    removeFromCart: (id) => http.delete(`/cart/${id}`),
    clearCart: () => http.delete('/cart')
  },

  // 用户相关
  user: {
    login: (data) => http.post('/user/login', data),
    getUserInfo: () => http.get('/user/info'),
    updateUserInfo: (data) => http.put('/user/info', data),
    getAddresses: () => http.get('/user/addresses'),
    addAddress: (data) => http.post('/user/addresses', data),
    updateAddress: (id, data) => http.put(`/user/addresses/${id}`, data),
    deleteAddress: (id) => http.delete(`/user/addresses/${id}`)
  },

  // 订单相关
  order: {
    createOrder: (data) => http.post('/orders', data),
    getOrders: (params) => http.get('/orders', params),
    getOrderDetail: (id) => http.get(`/orders/${id}`),
    cancelOrder: (id) => http.put(`/orders/${id}/cancel`),
    confirmOrder: (id) => http.put(`/orders/${id}/confirm`)
  }
}; 
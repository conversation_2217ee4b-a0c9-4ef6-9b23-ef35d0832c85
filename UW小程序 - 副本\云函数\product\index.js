// 云函数入口文件
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;
const $ = db.command.aggregate;

// 云函数入口函数
exports.main = async (event, context) => {
  const { action, data } = event;
  
  switch (action) {
    case 'get':
      return await getProduct(data);
    case 'list':
      return await getProductList(data);
    default:
      throw new Error('未知的操作类型');
  }
};

// 获取商品详情
async function getProduct(data) {
  const { id } = data;
  
  try {
    const product = await db.collection('products')
      .doc(id)
      .get();
      
    return {
      success: true,
      data: product.data
    };
  } catch (error) {
    return {
      success: false,
      message: error.message
    };
  }
}

// 获取商品列表
async function getProductList(data) {
  const { 
    page = 1, 
    limit = 10, 
    category,
    keyword,
    sort = 'createdAt'
  } = data;
  
  try {
    let query = db.collection('products')
      .where({
        status: 1
      });
      
    if (category) {
      query = query.where({
        category: category
      });
    }
    
    if (keyword) {
      query = query.where({
        name: db.RegExp({
          regexp: keyword,
          options: 'i'
        })
      });
    }
    
    const total = await query.count();
    
    const products = await query
      .skip((page - 1) * limit)
      .limit(limit)
      .orderBy(sort, 'desc')
      .get();
      
    return {
      success: true,
      data: {
        list: products.data,
        total: total.total,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    };
  } catch (error) {
    return {
      success: false,
      message: error.message
    };
  }
} 
/* 分包/product/detail/index.wxss */
.container {
  min-height: 100vh;
  background: #f5f5f5;
}

.product-swiper {
  width: 100%;
  height: 750rpx;
}

.product-swiper image {
  width: 100%;
  height: 100%;
}

.product-info {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.product-title {
  flex: 1;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-right: 20rpx;
}

.favorite-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.favorite-btn image {
  width: 48rpx;
  height: 48rpx;
}

.favorite-btn.active image {
  animation: favorite 0.3s ease;
}

@keyframes favorite {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.product-price {
  font-size: 36rpx;
  color: #ff4d4f;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.product-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* ... 保留其他现有样式 ... */
// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('Received event:', event)
  
  const { action, data } = event
  
  try {
    switch (action) {
      case 'get':
        return await getProduct(data)
      case 'list':
        return await getProductList(data)
      case 'add':
        return await addProduct(data)
      default:
        return {
          success: false,
          message: '未知操作'
        }
    }
  } catch (error) {
    console.error('Error:', error)
    return {
      success: false,
      message: error.message
    }
  }
}

async function getProduct({ id }) {
  const result = await db.collection('products').doc(id).get()
  return {
    success: true,
    data: result.data
  }
}

async function getProductList({ limit = 10, skip = 0 }) {
  const result = await db.collection('products')
    .skip(skip)
    .limit(limit)
    .where({
      status: 1
    })
    .get()
  
  return {
    success: true,
    data: result.data
  }
}

async function addProduct(productData) {
  try {
    const result = await db.collection('products').add({
      data: {
        ...productData,
        createTime: db.serverDate()
      }
    })
    return {
      success: true,
      data: result._id
    }
  } catch (error) {
    return {
      success: false,
      message: '添加商品失败',
      error
    }
  }
} 
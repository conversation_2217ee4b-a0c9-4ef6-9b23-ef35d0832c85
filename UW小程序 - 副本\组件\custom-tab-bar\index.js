Component({
  data: {
    selected: 0,
    color: "#999999",
    selectedColor: "#333333",
    list: [
      {
        pagePath: "/页面/index/index",
        text: "品牌中心",
        iconPath: "/图片/品牌中心-未选中.png",
        selectedIconPath: "/图片/品牌中心-选中.png"
      },
      {
        pagePath: "/页面/shop/shop",
        text: "品牌商城",
        iconPath: "/图片/品牌商城-未选中.png",
        selectedIconPath: "/图片/品牌商城-选中.png"
      },
      {
        pagePath: "/页面/profile/profile",
        text: "个人中心",
        iconPath: "/图片/个人中心-未选中.png",
        selectedIconPath: "/图片/个人中心-选中.png"
      }
    ]
  },

  methods: {
    switchTab(e) {
      const data = e.currentTarget.dataset
      const url = data.path
      wx.switchTab({ url })
      this.setData({
        selected: data.index
      })
    }
  }
}) 
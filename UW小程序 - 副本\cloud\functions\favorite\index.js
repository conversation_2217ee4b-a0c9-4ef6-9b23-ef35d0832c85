// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  console.log('Received event:', event)
  
  const { action, data } = event
  const wxContext = cloud.getWXContext()
  const userId = wxContext.OPENID

  try {
    switch (action) {
      case 'add':
        return await addFavorite(userId, data)
      case 'remove':
        return await removeFavorite(userId, data)
      case 'check':
        return await checkFavorite(userId, data)
      default:
        return {
          success: false,
          message: '未知操作'
        }
    }
  } catch (error) {
    console.error('Error:', error)
    return {
      success: false,
      message: error.message
    }
  }
}

async function addFavorite(userId, { productId }) {
  await db.collection('favorites').add({
    data: {
      userId,
      productId,
      createTime: db.serverDate()
    }
  })
  return {
    success: true,
    message: '收藏成功'
  }
}

async function removeFavorite(userId, { productId }) {
  await db.collection('favorites')
    .where({
      userId,
      productId
    })
    .remove()
  return {
    success: true,
    message: '取消收藏成功'
  }
}

async function checkFavorite(userId, { productId }) {
  const result = await db.collection('favorites')
    .where({
      userId,
      productId
    })
    .get()
  return {
    success: true,
    data: {
      isFavorite: result.data.length > 0
    }
  }
} 
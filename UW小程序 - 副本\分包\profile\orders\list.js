import { api } from '../../../工具/api'
import { showToast, showLoading, hideLoading } from '../../../工具/util'

Page({
  data: {
    tabs: ['全部', '待付款', '待发货', '待收货', '已完成'],
    currentTab: 0,
    orderList: [],
    loading: false,
    pageNum: 1,
    pageSize: 10,
    hasMore: true
  },

  onLoad(options) {
    if (options.type) {
      const index = parseInt(options.type)
      this.setData({ currentTab: index })
    }
    this.loadOrders()
  },

  async loadOrders(isLoadMore = false) {
    if (this.data.loading) return
    
    try {
      if (!isLoadMore) {
        showLoading()
      }
      
      this.setData({ loading: true })
      
      const res = await api.order.getOrders({
        status: this.data.currentTab || '',
        pageNum: this.data.pageNum,
        pageSize: this.data.pageSize
      })
      
      if (res.success) {
        const newList = isLoadMore ? 
          [...this.data.orderList, ...res.data.list] : 
          res.data.list
          
        this.setData({
          orderList: newList,
          loading: false,
          hasMore: res.data.total > newList.length
        })
      }
    } catch (error) {
      console.error('获取订单列表失败：', error)
      showToast('获取订单列表失败')
      this.setData({ loading: false })
    } finally {
      if (!isLoadMore) {
        hideLoading()
      }
    }
  },

  switchTab(e) {
    const index = e.currentTarget.dataset.index
    if (index === this.data.currentTab) return
    
    this.setData({
      currentTab: index,
      pageNum: 1,
      orderList: [],
      hasMore: true
    })
    
    this.loadOrders()
  },

  async onPullDownRefresh() {
    this.setData({
      pageNum: 1,
      orderList: [],
      hasMore: true
    })
    
    await this.loadOrders()
    wx.stopPullDownRefresh()
  },

  async onReachBottom() {
    if (!this.data.hasMore || this.data.loading) return
    
    this.setData({
      pageNum: this.data.pageNum + 1
    })
    
    await this.loadOrders(true)
  },

  // 查看订单详情
  viewOrderDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/分包/profile/orders/detail?id=${id}`
    })
  },

  // 取消订单
  async cancelOrder(e) {
    const { id } = e.currentTarget.dataset
    try {
      const res = await api.order.cancelOrder(id)
      if (res.success) {
        showToast('取消成功', 'success')
        this.refreshCurrentOrder(id)
      }
    } catch (error) {
      console.error('取消订单失败：', error)
      showToast('取消失败')
    }
  },

  // 确认收货
  async confirmOrder(e) {
    const { id } = e.currentTarget.dataset
    try {
      const res = await api.order.confirmOrder(id)
      if (res.success) {
        showToast('确认成功', 'success')
        this.refreshCurrentOrder(id)
      }
    } catch (error) {
      console.error('确认收货失败：', error)
      showToast('确认失败')
    }
  },

  // 刷新当前订单
  refreshCurrentOrder(orderId) {
    const orderList = this.data.orderList.map(order => {
      if (order.id === orderId) {
        return {
          ...order,
          status: order.status === 1 ? 0 : order.status + 1
        }
      }
      return order
    })
    
    this.setData({ orderList })
  }
}) 
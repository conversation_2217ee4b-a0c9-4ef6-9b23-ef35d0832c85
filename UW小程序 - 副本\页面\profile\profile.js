Page({
  data: {
    isLogin: false,
    userInfo: null
  },

  getPhoneNumber(e) {
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      const randomNum = Math.floor(10000 + Math.random() * 90000); // 生成5位随机数
      const phoneNumber = e.detail.code // 实际项目中需要通过后端解密
      
      const loginInfo = {
        nickName: 'U粉' + randomNum,
        avatarUrl: '/图片/未登录头像.png',
        level: 'Lv1',
        phone: phoneNumber,
        gender: 1,
        age: ''
      }
      
      wx.setStorageSync('userInfo', loginInfo)
      this.setData({
        isLogin: true,
        userInfo: loginInfo
      }, () => {
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })
      })
    }
  },

  goToUserInfo() {
    if (!this.data.isLogin) return
    wx.navigateTo({
      url: '/分包/profile/userInfo/index'
    })
  },

  onShow() {
    // 每次页面显示时检查更新用户信息
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({
        isLogin: true,
        userInfo
      })
    }
  },

  viewAllOrders() {
    if (!this.data.isLogin) return this.goToUserInfo()
    wx.navigateTo({
      url: '/分包/profile/orders/list'
    })
  },

  goToOrders(e) {
    if (!this.data.isLogin) return this.goToUserInfo()
    const type = e.currentTarget.dataset.type
    wx.navigateTo({
      url: `/分包/profile/orders/list?type=${type}`
    })
  },

  goToCoupons() {
    if (!this.data.isLogin) return this.goToUserInfo()
    wx.navigateTo({url: '/分包/profile/coupons/index'})
  },

  goToAddress() {
    if (!this.data.isLogin) return this.goToUserInfo()
    wx.navigateTo({url: '/分包/profile/address/list'})
  },

  contactService() {
    // 调用客服功能
  },

  goToAgreement() {
    wx.navigateTo({
      url: '/分包/profile/agreement/index'
    })
  },

  goToAbout() {
    wx.navigateTo({
      url: '/分包/profile/about/index'
    })
  },

  // 跳转到收藏列表
  goToFavorites() {
    if (!this.data.isLogin) return this.goToUserInfo()
    wx.navigateTo({
      url: '/分包/profile/favorite/index'
    })
  },
}) 
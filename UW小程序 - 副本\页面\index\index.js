// index.js

const { http } = require('../../工具/request.js');
const { showToast, showLoading, hideLoading } = require('../../工具/util.js');
const store = require('../../状态管理/store.js');

Page({
  data: {
    bannerList: [],
    productList: [],
    loading: false
  },

  onLoad() {
    this.loadBanners();
    this.loadProducts();
  },

  // 加载轮播图数据
  async loadBanners() {
    try {
      const banners = await http.get('/banners');
      this.setData({ bannerList: banners });
    } catch (error) {
      showToast('加载轮播图失败');
      console.error('加载轮播图失败:', error);
    }
  },

  // 加载商品列表
  async loadProducts() {
    if (this.data.loading) return;
    
    try {
      showLoading();
      this.setData({ loading: true });
      
      const products = await http.get('/products', {
        page: 1,
        limit: 10
      });
      
      this.setData({ 
        productList: products,
        loading: false
      });
    } catch (error) {
      this.setData({ loading: false });
      showToast('加载商品列表失败');
      console.error('加载商品列表失败:', error);
    } finally {
      hideLoading();
    }
  },

  // 点击商品
  onProductTap(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/页面/product/detail/index?id=${id}`
    });
  },

  // 下拉刷新
  async onPullDownRefresh() {
    try {
      await Promise.all([
        this.loadBanners(),
        this.loadProducts()
      ]);
    } finally {
      wx.stopPullDownRefresh();
    }
  }
});

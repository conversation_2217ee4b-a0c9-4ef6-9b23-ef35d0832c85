// 分包/profile/agreement/index.js
import { api } from '../../../工具/api'
import { showToast, showLoading, hideLoading } from '../../../工具/util'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    content: '',
    loading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadAgreement()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  async loadAgreement() {
    try {
      showLoading()
      this.setData({ loading: true })
      
      const res = await api.user.getAgreement()
      if (res.success) {
        this.setData({ 
          content: res.data.content,
          loading: false
        })
      }
    } catch (error) {
      console.error('获取用户协议失败：', error)
      showToast('获取用户协议失败')
      this.setData({ loading: false })
    } finally {
      hideLoading()
    }
  }
})
.container {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

.form {
  background: #fff;
  padding: 0 30rpx;
  margin-top: 20rpx;
}

.form-item {
  display: flex;
  align-items: flex-start;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 160rpx;
  font-size: 28rpx;
  color: #333;
  padding-top: 4rpx;
}

.input {
  flex: 1;
  height: 44rpx;
  font-size: 28rpx;
  color: #333;
}

.textarea {
  flex: 1;
  height: 120rpx;
  font-size: 28rpx;
  color: #333;
}

.picker {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.picker.placeholder {
  color: #999;
}

.switch-item {
  justify-content: space-between;
}

/* 底部按钮样式 */
.bottom-btn {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
}

.save-btn {
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: #000;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
} 
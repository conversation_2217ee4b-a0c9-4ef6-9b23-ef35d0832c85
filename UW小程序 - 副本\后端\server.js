const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const morgan = require('morgan');
const winston = require('winston');
const config = require('./config');
const axios = require('axios');
const routes = require('./routes');
const errorHandler = require('./middleware/errorHandler');

// 配置日志
const logger = winston.createLogger({
  level: config.log.level,
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: config.log.filename }),
    new winston.transports.Console()
  ]
});

const app = express();

// 中间件
app.use(cors(config.cors));
app.use(express.json());
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));

// 数据库连接
mongoose.connect(config.mongodb.uri, config.mongodb.options)
  .then(() => {
    logger.info('MongoDB 连接成功');
  })
  .catch(err => {
    logger.error('MongoDB 连接失败:', err);
    process.exit(1);
  });

// API路由
app.use('/api', routes);

// 基础路由测试
app.get('/', (req, res) => {
  res.json({ message: '服务器正在运行' });
});

// 测试路由
app.get('/test-wx-token', async (req, res) => {
  try {
    logger.info('正在直接测试微信API...');
    const response = await axios.get(
      'https://api.weixin.qq.com/cgi-bin/token',
      {
        params: {
          grant_type: 'client_credential',
          appid: config.weixin.appId,
          secret: config.weixin.appSecret
        }
      }
    );
    logger.info('微信API直接测试响应:', response.data);
    res.json(response.data);
  } catch (error) {
    logger.error('微信API直接测试失败:', error.response ? error.response.data : error.message);
    res.status(500).json({
      error: '微信API直接测试失败',
      details: error.response ? error.response.data : error.message
    });
  }
});

// 微信API代理
app.use('/wx-api/cgi-bin/token', async (req, res) => {
  try {
    const url = new URL(req.url, 'https://api.weixin.qq.com');
    logger.info('正在请求微信API:', url.toString());
    const response = await axios.get(url.toString());
    logger.info('微信API响应:', response.data);
    
    if (response.data.errcode) {
      logger.error('微信API返回错误:', response.data);
      return res.status(400).json(response.data);
    }
    
    res.json(response.data);
  } catch (error) {
    logger.error('微信API请求失败:', error.response ? error.response.data : error.message);
    res.status(500).json({ 
      error: '微信API请求失败',
      details: error.response ? error.response.data : error.message 
    });
  }
});

// 错误处理
app.use(errorHandler);

const server = app.listen(config.port, '0.0.0.0', (error) => {
  if (error) {
    logger.error('服务器启动失败:', error);
    process.exit(1);
  }
  logger.info(`服务器运行在端口 ${config.port}`);
});

// 优雅关闭
const gracefulShutdown = async () => {
  logger.info('正在关闭服务器...');
  try {
    await new Promise((resolve) => server.close(resolve));
    await mongoose.connection.close();
    logger.info('服务器已安全关闭');
    process.exit(0);
  } catch (err) {
    logger.error('关闭过程中发生错误:', err);
    process.exit(1);
  }
};

// 信号处理
process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

// 错误处理
process.on('uncaughtException', (err) => {
  logger.error('未捕获的异常:', err);
  gracefulShutdown();
});

process.on('unhandledRejection', (reason) => {
  logger.error('未处理的Promise拒绝:', reason);
  gracefulShutdown();
}); 
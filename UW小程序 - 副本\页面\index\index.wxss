/**index.wxss**/
.nav-placeholder {
  height: calc(44px + env(safe-area-inset-top));
  width: 100%;
}

.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  background: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
}

.content text {
  display: block;
  margin-bottom: 10rpx;
}

.search-box {
  margin: 20rpx 30rpx;
  height: 80rpx;
  background: #ffffff;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.search-icon {
  margin-right: 20rpx;
  font-size: 32rpx;
}

.search-box input {
  flex: 1;
  height: 100%;
  font-size: 28rpx;
}

.placeholder {
  color: #999;
}

.main-content {
  flex: 1;
  padding: 30rpx;
  height: calc(100vh - 180rpx - env(safe-area-inset-top));
}

.banner-section {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 20rpx;
  padding: 40rpx;
  color: #ffffff;
  margin-bottom: 40rpx;
}

.banner-content {
  position: relative;
}

.banner-text {
  margin-bottom: 30rpx;
}

.banner-title {
  font-size: 40rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.banner-subtitle {
  font-size: 36rpx;
  display: block;
  margin-bottom: 10rpx;
}

.banner-desc {
  font-size: 28rpx;
  opacity: 0.8;
  display: block;
}

.banner-image {
  width: 100%;
  height: 500rpx;
  margin: 20rpx 0;
}

.banner-tag {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
}

.series-section {
  margin-top: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: var(--text-color);
}

.series-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.series-item {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding: 20rpx;
}

.series-item image {
  width: 100%;
  height: 200rpx;
  object-fit: cover;
}

.series-info {
  padding: 20rpx;
}

.series-name {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-color);
  display: block;
  margin-bottom: 8rpx;
}

.series-desc {
  font-size: 24rpx;
  color: var(--text-color-light);
  display: block;
}

swiper {
  width: 100%;
  height: 100vh;
}

swiper-item image {
  width: 100%;
  height: 100%;
}

.banner {
  width: 100%;
  height: 400rpx;
}

.banner image {
  width: 100%;
  height: 100%;
}

.wx-swiper-dot {
  width: 40rpx !important;
  height: 4rpx !important;
  border-radius: 0 !important;
  background: rgba(255, 255, 255, 0.3) !important;
}

.wx-swiper-dot-active {
  background: #ffffff !important;
}

/* 商品列表 */
.product-list {
  padding: 20rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.product-item {
  width: 345rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.product-image {
  width: 100%;
  height: 345rpx;
}

.product-info {
  padding: 16rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.product-price {
  font-size: 32rpx;
  color: #ff4444;
  font-weight: bold;
  margin-top: 8rpx;
  display: block;
}

/* 加载中提示 */
.loading {
  text-align: center;
  padding: 30rpx;
  color: #999;
  font-size: 24rpx;
}

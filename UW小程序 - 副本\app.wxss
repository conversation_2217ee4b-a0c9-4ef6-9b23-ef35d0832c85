/**app.wxss**/
.container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
} 

page {
  --primary-color: #8B4513;
  --secondary-color: #D2691E;
  --text-color: #333333;
  --text-color-light: #666666;
  --background-color: #f5f5f5;
  
  font-size: 28rpx;
  color: var(--text-color);
  background: var(--background-color);
  min-height: 100vh;
}

/* 通用样式 */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
} 

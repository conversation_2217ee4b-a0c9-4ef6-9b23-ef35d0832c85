const Order = require('../models/Order');
const Product = require('../models/Product');
const { AppError } = require('../middleware/errorHandler');

// 生成订单号
const generateOrderNo = () => {
  const now = new Date();
  return 'UW' + 
    now.getFullYear().toString().slice(-2) +
    (now.getMonth() + 1).toString().padStart(2, '0') +
    now.getDate().toString().padStart(2, '0') +
    now.getHours().toString().padStart(2, '0') +
    now.getMinutes().toString().padStart(2, '0') +
    now.getSeconds().toString().padStart(2, '0') +
    Math.floor(Math.random() * 1000).toString().padStart(3, '0');
};

exports.createOrder = async (req, res) => {
  try {
    const { userId } = req;
    const { products, address } = req.body;
    
    // 计算总金额
    let totalAmount = 0;
    const orderProducts = [];
    
    for (const item of products) {
      const product = await Product.findById(item.productId);
      if (!product) {
        throw new AppError('商品不存在', 404);
      }
      
      const sku = product.skus.find(s => s._id.toString() === item.skuId);
      if (!sku) {
        throw new AppError('商品规格不存在', 404);
      }
      
      if (sku.stock < item.quantity) {
        throw new AppError('商品库存不足', 400);
      }
      
      totalAmount += sku.price * item.quantity;
      orderProducts.push({
        product: product._id,
        sku: {
          name: sku.name,
          price: sku.price
        },
        quantity: item.quantity,
        price: sku.price
      });
      
      // 更新库存
      sku.stock -= item.quantity;
      await product.save();
    }
    
    const order = await Order.create({
      user: userId,
      orderNo: generateOrderNo(),
      products: orderProducts,
      totalAmount,
      address,
      status: 1
    });
    
    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    throw new AppError(error.message);
  }
};

exports.getOrders = async (req, res) => {
  try {
    const { userId } = req;
    const { status, page = 1, limit = 10 } = req.query;
    
    const query = { user: userId };
    if (status) {
      query.status = parseInt(status);
    }
    
    const orders = await Order.find(query)
      .populate('products.product')
      .skip((page - 1) * limit)
      .limit(parseInt(limit))
      .sort({ createdAt: -1 });
      
    const total = await Order.countDocuments(query);
    
    res.json({
      success: true,
      data: {
        list: orders,
        total,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    throw new AppError(error.message);
  }
};

exports.getOrderDetail = async (req, res) => {
  try {
    const { userId } = req;
    const { id } = req.params;
    
    const order = await Order.findOne({
      _id: id,
      user: userId
    }).populate('products.product');
    
    if (!order) {
      throw new AppError('订单不存在', 404);
    }
    
    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    throw new AppError(error.message);
  }
};

exports.cancelOrder = async (req, res) => {
  try {
    const { userId } = req;
    const { id } = req.params;
    
    const order = await Order.findOne({
      _id: id,
      user: userId,
      status: 1
    });
    
    if (!order) {
      throw new AppError('订单不存在或无法取消', 404);
    }
    
    // 恢复库存
    for (const item of order.products) {
      const product = await Product.findById(item.product);
      if (product) {
        const sku = product.skus.find(s => s.name === item.sku.name);
        if (sku) {
          sku.stock += item.quantity;
          await product.save();
        }
      }
    }
    
    order.status = 0;
    await order.save();
    
    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    throw new AppError(error.message);
  }
};

exports.confirmOrder = async (req, res) => {
  try {
    const { userId } = req;
    const { id } = req.params;
    
    const order = await Order.findOne({
      _id: id,
      user: userId,
      status: 3
    });
    
    if (!order) {
      throw new AppError('订单不存在或无法确认收货', 404);
    }
    
    order.status = 4;
    order.completionTime = new Date();
    await order.save();
    
    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    throw new AppError(error.message);
  }
}; 
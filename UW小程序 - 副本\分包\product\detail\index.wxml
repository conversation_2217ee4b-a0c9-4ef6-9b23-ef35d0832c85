<!--分包/product/detail/index.wxml-->
<view class="container">
  <!-- 商品轮播图 -->
  <swiper class="product-swiper" indicator-dots circular>
    <swiper-item wx:for="{{product.images}}" wx:key="*this">
      <image src="{{item}}" mode="aspectFill"></image>
    </swiper-item>
  </swiper>

  <!-- 商品信息 -->
  <view class="product-info">
    <view class="product-header">
      <view class="product-title">{{product.name}}</view>
      <view class="favorite-btn {{isFavorite ? 'active' : ''}}" bindtap="toggleFavorite">
        <image src="/图片/{{isFavorite ? '已收藏' : '收藏'}}.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="product-price">¥{{selectedSku.price}}</view>
    <view class="product-desc">{{product.description}}</view>
  </view>

  <!-- ... 保留其他现有代码 ... -->
</view>
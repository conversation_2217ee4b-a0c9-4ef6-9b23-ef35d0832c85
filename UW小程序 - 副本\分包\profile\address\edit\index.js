import { api } from '../../../../工具/api'
import { showToast, showLoading, hideLoading } from '../../../../工具/util'

Page({
  data: {
    id: '',
    name: '',
    phone: '',
    region: ['请选择', '请选择', '请选择'],
    address: '',
    isDefault: false
  },

  onLoad(options) {
    if (options.id) {
      this.setData({ id: options.id })
      this.loadAddressDetail()
    }
  },

  async loadAddressDetail() {
    try {
      showLoading()
      const res = await api.user.getAddressDetail(this.data.id)
      if (res.success) {
        const { name, phone, province, city, district, address, isDefault } = res.data
        this.setData({
          name,
          phone,
          region: [province, city, district],
          address,
          isDefault
        })
      }
    } catch (error) {
      console.error('获取地址详情失败：', error)
      showToast('获取地址详情失败')
    } finally {
      hideLoading()
    }
  },

  // 输入框变化
  onInput(e) {
    const { field } = e.currentTarget.dataset
    this.setData({
      [field]: e.detail.value
    })
  },

  // 地区选择
  bindRegionChange(e) {
    this.setData({
      region: e.detail.value
    })
  },

  // 默认地址开关
  switchChange(e) {
    this.setData({
      isDefault: e.detail.value
    })
  },

  // 保存地址
  async saveAddress() {
    const { name, phone, region, address, isDefault } = this.data
    
    // 表单验证
    if (!name) {
      return showToast('请输入收货人姓名')
    }
    if (!phone) {
      return showToast('请输入手机号码')
    }
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      return showToast('手机号码格式不正确')
    }
    if (region[0] === '请选择') {
      return showToast('请选择所在地区')
    }
    if (!address) {
      return showToast('请输入详细地址')
    }

    try {
      showLoading()
      const data = {
        name,
        phone,
        province: region[0],
        city: region[1],
        district: region[2],
        address,
        isDefault
      }

      let res
      if (this.data.id) {
        res = await api.user.updateAddress(this.data.id, data)
      } else {
        res = await api.user.addAddress(data)
      }

      if (res.success) {
        showToast('保存成功', 'success')
        // 返回上一页并刷新列表
        const pages = getCurrentPages()
        const prevPage = pages[pages.length - 2]
        prevPage.loadAddressList()
        
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      console.error('保存地址失败：', error)
      showToast('保存失败')
    } finally {
      hideLoading()
    }
  }
}) 
const { api } = require('../../../工具/api')
const { showToast, showLoading, hideLoading, showModal } = require('../../../工具/util')
const store = require('../../../状态管理/store')

Page({
  data: {
    favoriteList: [],
    loading: false,
    pageNum: 1,
    pageSize: 10,
    hasMore: true
  },

  onLoad() {
    // 检查登录状态
    if (!store.getState().userInfo) {
      wx.redirectTo({
        url: '/页面/profile/profile'
      })
      return
    }
    this.loadFavorites()
  },

  async loadFavorites(isLoadMore = false) {
    if (this.data.loading) return
    
    try {
      if (!isLoadMore) {
        showLoading()
      }
      
      this.setData({ loading: true })
      
      const res = await api.favorite.list({
        pageNum: this.data.pageNum,
        pageSize: this.data.pageSize
      })
      
      if (res.success) {
        const newList = isLoadMore ? 
          [...this.data.favoriteList, ...res.data.list] : 
          res.data.list
          
        this.setData({
          favoriteList: newList,
          loading: false,
          hasMore: res.data.total > newList.length
        })
      }
    } catch (error) {
      console.error('获取收藏列表失败：', error)
      showToast('获取收藏列表失败')
      this.setData({ loading: false })
    } finally {
      if (!isLoadMore) {
        hideLoading()
      }
    }
  },

  onPullDownRefresh() {
    this.setData({
      pageNum: 1,
      favoriteList: [],
      hasMore: true
    })
    
    this.loadFavorites().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  onReachBottom() {
    if (!this.data.hasMore || this.data.loading) return
    
    this.setData({
      pageNum: this.data.pageNum + 1
    })
    
    this.loadFavorites(true)
  },

  // 取消收藏
  async cancelFavorite(e) {
    const { id } = e.currentTarget.dataset
    
    try {
      const confirmed = await showModal({
        title: '提示',
        content: '确定要取消收藏该商品吗？',
        confirmText: '确定'
      })
      
      if (!confirmed) return
      
      showLoading('取消中...')
      const res = await api.favorite.remove(id)
      
      if (res.success) {
        showToast('取消成功', 'success')
        // 更新列表
        const favoriteList = this.data.favoriteList.filter(item => item.id !== id)
        this.setData({ favoriteList })
      }
    } catch (error) {
      console.error('取消收藏失败：', error)
      showToast('取消失败')
    } finally {
      hideLoading()
    }
  },

  // 跳转到商品详情
  goToDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/分包/product/detail/index?id=${id}`
    })
  }
}) 
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const config = {
  // 服务器配置
  port: process.env.PORT || 3000,
  nodeEnv: process.env.NODE_ENV || 'development',

  // 数据库配置
  mongodb: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/uw_miniprogram',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true
    }
  },

  // 微信小程序配置
  weixin: {
    appId: process.env.WX_APP_ID,
    appSecret: process.env.WX_APP_SECRET
  },

  // 日志配置
  log: {
    level: process.env.LOG_LEVEL || 'info',
    filename: path.join(__dirname, '../logs/app.log')
  },

  // 跨域配置
  cors: {
    origin: process.env.CORS_ORIGIN || '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
  }
};

module.exports = config; 
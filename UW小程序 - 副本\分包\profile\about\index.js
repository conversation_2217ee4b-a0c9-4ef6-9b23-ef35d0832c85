// 分包/profile/about/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    version: '1.0.0',
    company: 'UW品牌中心',
    copyright: '© 2024 UW品牌中心 版权所有',
    contact: {
      phone: '400-xxx-xxxx',
      email: '<EMAIL>',
      address: '北京市朝阳区xxx大厦'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 拨打电话
  callService() {
    wx.makePhoneCall({
      phoneNumber: this.data.contact.phone
    })
  },

  // 复制邮箱
  copyEmail() {
    wx.setClipboardData({
      data: this.data.contact.email,
      success: () => {
        wx.showToast({
          title: '邮箱已复制',
          icon: 'success'
        })
      }
    })
  }
})
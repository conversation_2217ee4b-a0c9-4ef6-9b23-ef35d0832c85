{"appid": "wxf57755081128c53d", "compileType": "miniprogram", "libVersion": "development", "cloudfunctionRoot": "cloud/functions/", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "uploadWithSourceMap": true, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "compileWorklet": false, "uglifyFileName": false, "packNpmManually": false, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "projectArchitecture": "miniProgram", "simulatorPluginLibVersion": {}}
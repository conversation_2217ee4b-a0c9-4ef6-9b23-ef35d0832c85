/* 分包/profile/orders/list.wxss */
.container {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

/* 标签页样式 */
.tabs {
  display: flex;
  background: #fff;
  padding: 0 30rpx;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1;
}

.tab-item {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.tab-item text {
  font-size: 28rpx;
  color: #333;
}

.tab-item.active text {
  color: #000;
  font-weight: bold;
}

.tab-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: #000;
  border-radius: 2rpx;
}

/* 订单列表样式 */
.order-list {
  flex: 1;
  margin-top: 88rpx;
  padding: 20rpx;
}

.order-item {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.order-no {
  font-size: 24rpx;
  color: #666;
}

.order-status {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.order-content {
  border-top: 1rpx solid #f5f5f5;
  border-bottom: 1rpx solid #f5f5f5;
  padding: 20rpx 0;
}

.product-list {
  margin-bottom: 20rpx;
}

.product-item {
  display: flex;
  margin-bottom: 20rpx;
}

.product-item:last-child {
  margin-bottom: 0;
}

.product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.product-sku {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.product-price-count {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.product-count {
  font-size: 24rpx;
  color: #999;
}

.order-summary {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-top: 20rpx;
}

.total-count {
  font-size: 24rpx;
  color: #999;
  margin-right: 20rpx;
}

.total-amount {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-actions {
  display: flex;
  align-items: center;
}

.action-btn {
  margin-left: 20rpx;
  height: 60rpx;
  line-height: 60rpx;
  padding: 0 30rpx;
  font-size: 26rpx;
  border-radius: 30rpx;
  background: #fff;
}

.action-btn.cancel {
  color: #666;
  border: 1rpx solid #ddd;
}

.action-btn.primary {
  color: #fff;
  background: #000;
  border: none;
}

/* 加载状态样式 */
.loading-status {
  text-align: center;
  padding: 30rpx 0;
}

.loading, .no-more {
  font-size: 24rpx;
  color: #999;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
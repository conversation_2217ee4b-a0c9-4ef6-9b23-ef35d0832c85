<!--分包/profile/agreement/index.wxml-->
<view class="container">
  <view class="content">
    <view class="title">用户协议</view>
    <rich-text class="agreement-content" nodes="{{content}}"></rich-text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <view class="loading">加载中...</view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && !content}}">
    <image class="empty-icon" src="/图片/帮助图标.png"></image>
    <text class="empty-text">暂无内容</text>
  </view>
</view>
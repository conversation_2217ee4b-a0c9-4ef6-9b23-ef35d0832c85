const express = require('express');
const router = express.Router();
const cartController = require('../controllers/cartController');
const auth = require('../middleware/auth');

// 获取购物车列表
router.get('/', auth, cartController.getCartList);

// 添加到购物车
router.post('/', auth, cartController.addToCart);

// 更新购物车商品
router.put('/:id', auth, cartController.updateCartItem);

// 删除购物车商品
router.delete('/:id', auth, cartController.removeFromCart);

// 清空购物车
router.delete('/', auth, cartController.clearCart);

module.exports = router; 